[{"Prompt": "Traditional food of the Mid-Autumn Festival", "Explanation": "This refers to mooncakes, the round pastries filled with lotus seed paste or red bean paste", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 1, "Hint": "Mooncakes."}, {"Prompt": "Traditional game played during the Korean festival of Chuseok", "Explanation": "This refers to ssireum, a form of traditional Korean wrestling", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 2, "Hint": "Ssireum."}, {"Prompt": "Traditional activity during Easter in Western countries", "Explanation": "This refers to the Easter egg hunt", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 3, "Hint": "Easter egg hunt."}, {"Prompt": "Holiday celebrating the birth of <PERSON> Christ", "Explanation": "This refers to Christmas", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 4, "Hint": "Christmas."}, {"Prompt": "Traditional decoration for the Mexican Day of the Dead", "Explanation": "This refers to ofrendas, elaborate altars decorated with marigolds, candles, and offerings", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 5, "Hint": "<PERSON><PERSON><PERSON> decorated with marigolds, candles, and offerings."}, {"Prompt": "Symbolic animal associated with the Chinese New Year", "Explanation": "This refers to the dragon, a mythical creature representing power and good fortune", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 6, "Hint": "Dragon."}, {"Prompt": "A spooky night when children dress up and collect sweet treats", "Explanation": "This refers to Halloween, the image should show children in costumes collecting candy", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 7, "Hint": "Children in costumes collecting candy on Halloween night."}, {"Prompt": "Most mentioned character during Christmas", "Explanation": "This refers to Santa Claus", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 8, "Hint": "Santa Claus."}, {"Prompt": "Traditional food for the Lantern Festival in China", "Explanation": "This refers to tangyuan, glutinous rice balls", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 9, "Hint": "Tangyuan, glutinous rice balls."}, {"Prompt": "Show the most common outdoor activity associated with the Dragon Boat Festival", "Explanation": "This refers to dragon boat races, so the image should depict a race with long boats, synchronized paddling teams, a body of water, and festive surroundings", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 10, "Hint": "Dragon boat race with long boats, synchronized paddling teams, a body of water, and festive surroundings."}, {"Prompt": "Most commonly used tools during Diwali in India", "Explanation": "This refers to diyas (small oil lamps), so the photo should include diyas", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 11, "Hint": "Diyas (small oil lamps)."}, {"Prompt": "Popular festival among the Dai people in China", "Explanation": "This refers to the Songkran Festival", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 12, "Hint": "Songkran Festival."}, {"Prompt": "Traditional food for the Dragon Boat Festival in China", "Explanation": "This refers to zongzi, sticky rice wrapped in bamboo leaves", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 13, "Hint": "Zongzi, sticky rice wrapped in bamboo leaves."}, {"Prompt": "Halloween kids' favorite treat", "Explanation": "It refers to candy, which kids like to collect most", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 14, "Hint": "<PERSON>."}, {"Prompt": "Traditional activity during the Iranian festival of Nowruz", "Explanation": "This refers to haft-sin, a table setting with seven symbolic items starting with the letter 'sin' in Persian", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 15, "Hint": "Haft-sin table setting with seven symbolic items starting with the letter 'sin' in Persian."}, {"Prompt": "Traditional activity during the Indian festival of Holi", "Explanation": "This refers to playing with colored powders and water, celebrating the arrival of spring", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 16, "Hint": "Playing with colored powders and water, celebrating the arrival of spring."}, {"Prompt": "An autumnal harvest celebration in North America, where a large fowl takes center stage on the dining table", "Explanation": "This refers to Thanksgiving, so the image should show a roasted turkey surrounded by other food", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 17, "Hint": "A roasted turkey surrounded by other Thanksgiving food."}, {"Prompt": "Traditional dance performed during the Brazilian Carnival", "Explanation": "This refers to samba, a lively dance with African roots", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 18, "Hint": "Samba dance during Brazilian Carnival."}, {"Prompt": "The object most commonly admired during the Mid-Autumn Festival", "Explanation": "This refers to the moon, so the image should show a full or nearly full moon, possibly with elements suggesting the festival such as lanterns or family gatherings", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 19, "Hint": "Full moon with lanterns and a family gathering."}, {"Prompt": "Traditional food for Thanksgiving in the United States", "Explanation": "This refers to roasted turkey with sides like mashed potatoes", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 20, "Hint": "Roasted turkey with sides of mashed potatoes."}, {"Prompt": "Traditional costume worn during the German Oktoberfest", "Explanation": "This refers to lederhosen for men and dirndl for women, traditional Bavarian attire", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 21, "Hint": "Traditional Bavarian attire, including lederhosen for men and dirndl for women, worn during Oktoberfest."}, {"Prompt": "Traditional gift given during the Japanese festival of Setsubun", "Explanation": "This refers to mame-maki, the custom of scattering roasted soybeans to drive away evil spirits", "Category": "Cultural knowledge", "Subcategory": "Festival", "prompt_id": 22, "Hint": "Scattering roasted soybeans (mame-maki)."}, {"Prompt": "Most representative sport of South Africa", "Explanation": "This refers to rugby, so the photo should include the rugby ball", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 23, "Hint": "Rugby ball."}, {"Prompt": "A sport that ignites national fervor in Argentina, often associated with legendary players and historic rivalries", "Explanation": "This refers to football (soccer), so the image should include a football, and potentially Argentinian colors or fans", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 24, "Hint": "Football (soccer) with Argentinian colors or fans celebrating in the background."}, {"Prompt": "A very popular sport in the US with an oval shaped ball", "Explanation": "This refers to American football, so the photo should include the American football", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 25, "Hint": "American football."}, {"Prompt": "Show an image of the most popular ball sport in Japan, often associated with school teams and national tournaments", "Explanation": "The model should generate an image related to baseball", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 26, "Hint": "Baseball."}, {"Prompt": "Most representative sport of India", "Explanation": "This refers to cricket, so the photo should include the cricket ball", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 27, "Hint": "Cricket ball."}, {"Prompt": "A fast-moving team sport popular in Russia, known for its intense physicality", "Explanation": "This refers to ice hockey, so the image should include a hockey puck, and potentially ice and players", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 28, "Hint": "Ice hockey with a hockey puck, ice, and players."}, {"Prompt": "A sport deeply ingrained in Brazilian culture, known for its flair and passion on the field", "Explanation": "This refers to football (soccer), so the image should include a football, and potentially Brazilian colors or fans", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 29, "Hint": "Football with Brazilian colors or fans."}, {"Prompt": "Show the national sport of the United States", "Explanation": "This refers to baseball, so the image should depict a baseball game or elements associated with baseball, such as a bat, ball, or field", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 30, "Hint": "Baseball game or elements associated with baseball, such as a bat, ball, or field."}, {"Prompt": "Show the national sport of Japan", "Explanation": "This refers to Sumo wrestling, so the image should depict a Sumo wrestling match or elements associated with <PERSON><PERSON>, such as the wrestlers, the ring or traditional garments", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 31, "Hint": "Sumo wrestling match."}, {"Prompt": "Show a combat sport often practiced in Thailand", "Explanation": "This refers to Muay <PERSON>, so the image should depict two people engaging in combat techniques", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 32, "Hint": "<PERSON>ay Thai combat between two fighters."}, {"Prompt": "A winter sport often enjoyed in Switzerland, involving snow covered slopes", "Explanation": "This refers to skiing, so the photo should include a skier or ski equipment", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 33, "Hint": "Skiing involving a skier or ski equipment on snow-covered slopes."}, {"Prompt": "A bat-and-ball sport with a passionate following in Pakistan, often enjoyed by all ages", "Explanation": "This refers to cricket, so the image should include a cricket ball, and potentially Pakistani colours or a stadium", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 34, "Hint": "Cricket ball with Pakistani colors or a stadium."}, {"Prompt": "Show the most popular ball sport in Australia", "Explanation": "The model should generate an image related to Australian Rules Football", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 35, "Hint": "Australian Rules Football."}, {"Prompt": "A widely followed sport in the UK, often played with a round ball and passionate supporters", "Explanation": "This refers to football (soccer), so the photo should include the football", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 36, "Hint": "Football (soccer) with a round ball and passionate supporters."}, {"Prompt": "A physically demanding contact sport, deeply associated with New Zealand's national identity", "Explanation": "This refers to rugby, so the image should include a rugby ball and possibly a rugby pitch", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 37, "Hint": "Rugby ball on a rugby pitch."}, {"Prompt": "A highly technical and fast-paced sport in China, often showcasing incredible agility and precision", "Explanation": "This refers to table tennis, so the image should include a table tennis ball and paddle", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 38, "Hint": "Table tennis ball and paddle."}, {"Prompt": "A fast-paced racquet sport that is extremely popular in Indonesia, requiring agility and precision", "Explanation": "This refers to badminton, so the image should include a badminton shuttlecock and a racquet", "Category": "Cultural knowledge", "Subcategory": "Sports", "prompt_id": 39, "Hint": "Badminton shuttlecock and racquet."}, {"Prompt": "An image that demonstrates the act of seeking knowledge and guidance from a religious elder or leader in a Buddhist monastic context", "Explanation": "The model should generate an image of Buddhist monks consulting or getting a teaching from their master/teacher", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 40, "Hint": "Buddhist monks consulting their master or teacher."}, {"Prompt": "A representation of the divine trinity in the Christian tradition, often depicted through art and symbolism", "Explanation": "The model should generate an image representing the Holy Trinity in Christianity, often depicted as God the Father, God the Son, and <PERSON> the Holy Spirit", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 41, "Hint": "The Holy Trinity in Christianity, depicted as God the Father, God the Son, and God the Holy Spirit."}, {"Prompt": "An image of a commonly revered object in Christianity that often symbolizes sacrifice and faith", "Explanation": "The model should generate an image related to the Christian Cross", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 42, "Hint": "<PERSON>."}, {"Prompt": "Show an image of a type of large religious building that is often visited by Christians, and frequently includes a significant symbol as part of its architecture", "Explanation": "The model should generate an image related to a Christian church or cathedral with a prominent cross symbol in its architecture", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 43, "Hint": "Christian church or cathedral with a prominent cross symbol in its architecture."}, {"Prompt": "A figure central to the story of Islam, revered as the last prophet", "Explanation": "The model should generate an image that represents <PERSON>, with focus on respect and avoiding direct depiction of his face", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 44, "Hint": "A representation of <PERSON>, focusing on respectful symbols such as a glowing silhouette, calligraphy, or a serene landscape."}, {"Prompt": "An image showcasing an event that marks the beginning of the holiest month for Muslims, often with a traditional meal in the morning", "Explanation": "The model should generate an image depicting the '<PERSON><PERSON>or' meal, an important event in Ramadan", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 45, "Hint": "<PERSON><PERSON><PERSON> meal during Ramadan."}, {"Prompt": "Show an image of a type of domed religious structure that is often seen in Buddhist temples or sacred sites", "Explanation": "The model should generate an image related to a Buddhist stupa or pagoda", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 46, "Hint": "Buddhist stupa or pagoda."}, {"Prompt": "An object of veneration in Sikh temples, often seen as a source of wisdom and spiritual authority", "Explanation": "The model should generate an image of the Guru <PERSON>, the central religious scripture of Sikhism", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 47, "Hint": "<PERSON>."}, {"Prompt": "A sacred text often found in Jewish synagogues, containing teachings and stories that guide the faithful", "Explanation": "The model should generate an image of a Torah scroll, focusing on its ornate cover and handwritten text", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 48, "Hint": "Torah scroll with ornate cover and handwritten text."}, {"Prompt": "A key architectural feature that is often in the form of a tower, used by Muslims for call to prayer", "Explanation": "The model should generate an image of a Minaret, the tall slender tower in a Mosque", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 49, "Hint": "<PERSON><PERSON>."}, {"Prompt": "A geometric symbol often associated with Jewish identity and heritage", "Explanation": "The model should generate an image related to the Star of David", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 50, "Hint": "Star of David."}, {"Prompt": "An image of a circular emblem representing balance and harmony in Taoist philosophy", "Explanation": "The model should generate an image related to the Taijitu (Yin-Yang symbol)", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 51, "Hint": "Tai<PERSON><PERSON> (Yin-Yang symbol)."}, {"Prompt": "Show a structure commonly seen in Shinto shrines, often acting as a gateway to a sacred space", "Explanation": "The model should generate an image related to a Torii gate", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 52, "Hint": "Torii gate."}, {"Prompt": "Show an image of a figure that is often a central focus of worship and meditation in Buddhism", "Explanation": "The model should generate an image related to a Buddha statue or figure", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 53, "Hint": "Buddha statue."}, {"Prompt": "A sacred site for Muslims, with a cuboid structure at its center, a focal point for prayer", "Explanation": "The model should generate an image of the Kaaba in Mecca, showcasing its cuboid shape, the black cloth covering, and its importance as a sacred site for Muslims", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 54, "Hint": "Kaaba in Mecca, with its cuboid shape and black cloth covering, as a sacred site for Muslims."}, {"Prompt": "Show an image depicting a specific hand gesture used during prayer, often seen in congregations that follow a particular Abrahamic tradition", "Explanation": "The model should generate an image related to the 'priestly blessing' hand gesture common in Judaism, which includes the spreading of the fingers to form the letter Shin", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 55, "Hint": "The priestly blessing hand gesture common in Judaism, with fingers spread to form the letter Shin."}, {"Prompt": "A powerful symbol often used in Islamic art, often representing divine guidance", "Explanation": "The model should generate an image related to the crescent moon and star", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 56, "Hint": "Crescent moon and star."}, {"Prompt": "Show an image of a specific type of head covering worn by many Sikh men", "Explanation": "The model should generate an image related to the Sikh Turban or Dastar", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 57, "Hint": "Sikh <PERSON> (Dastar)."}, {"Prompt": "An image of a specific type of clothing often worn by Jewish men when they pray", "Explanation": "The model should generate an image related to a Jewish Tallit or prayer shawl", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 58, "Hint": "Jewish Tallit or prayer shawl."}, {"Prompt": "A place where sacred rituals and ceremonies often take place in Hinduism, a center of worship and community", "Explanation": "The model should generate an image representing a Hindu temple, showcasing the architecture, the deity statues, and the atmosphere of worship", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 59, "Hint": "Hindu temple with traditional architecture, deity statues, and an atmosphere of worship."}, {"Prompt": "A sacred symbol often used in Hindu practices, representing the essence of the universe", "Explanation": "The model should generate an image related to the Om symbol", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 60, "Hint": "Om symbol."}, {"Prompt": "A sacred symbol used in Jainism, representing the path to liberation and enlightenment", "Explanation": "The model should generate an image of the Jain symbol, which includes the hand, the wheel, and the three dots", "Category": "Cultural knowledge", "Subcategory": "Religion", "prompt_id": 61, "Hint": "Jain symbol with a hand, wheel, and three dots."}, {"Prompt": "Most representative craft of India", "Explanation": "This refers to Indian handwoven textiles, so the photo should include a colorful sari or textile pattern", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 62, "Hint": "Colorful sari or textile pattern."}, {"Prompt": "A craft that celebrates the beauty of natural elements through its use of fine, polished stones in a particular country in South America", "Explanation": "This refers to Peruvian Stone Carving, so the photo should include a photo of finely crafted stone objects or sculptures with cultural motifs", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 63, "Hint": "Peruvian Stone Carving with finely crafted stone objects or sculptures featuring cultural motifs."}, {"Prompt": "The playful craft that embodies Russian cultural charm", "Explanation": "This refers to Russian nesting dolls (Matryoshka), so the photo should include a set of colorful Matryoshka dolls", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 64, "Hint": "Russian nesting dolls (Matryoshka), a set of colorful wooden dolls."}, {"Prompt": "A craft renowned for its glass artistry and elaborate chandeliers in the Venetian city", "Explanation": "This refers to Murano glass, so the photo should include a detailed image of glass objects or a Murano chandelier", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 65, "Hint": "Murano glass objects or a detailed Murano chandelier."}, {"Prompt": "A craft involving the application of enamel on metal surfaces that is popular in a particular region of Southeast Asia", "Explanation": "This refers to Cloisonné, so the photo should include an image of an object decorated with cloisonné enamel, showcasing intricate designs and vibrant colors, commonly found in Southeast Asia", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 66, "Hint": "An object decorated with cloisonné enamel, showcasing intricate designs and vibrant colors, commonly found in Southeast Asia."}, {"Prompt": "A craft celebrated in Japanese culture, known for its intricate folds and paper art", "Explanation": "This refers to Origami, so the photo should include an image of folded paper sculptures or objects", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 67, "Hint": "Origami with folded paper sculptures or objects."}, {"Prompt": "The iconic craft that captures the essence of French elegance", "Explanation": "This refers to French perfume, so the photo should include an elegant perfume bottle", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 68, "Hint": "An elegant French perfume bottle."}, {"Prompt": "A craft known for its use of silver, and intricate designs in a particular country in South America", "Explanation": "This refers to Mexican silver craft, so the photo should include a photograph showing finely crafted silver objects or jewelry with distinctive motifs and patterns", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 69, "Hint": "Mexican silvercraft showing finely crafted silver objects or jewelry with distinctive motifs and patterns."}, {"Prompt": "The intricate craft that highlights the artistry of Persian weaving", "Explanation": "This refers to Persian carpets, so the photo should include a colorful, intricate carpet with traditional patterns", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 70, "Hint": "Persian carpet with colorful, intricate traditional patterns."}, {"Prompt": "A craft recognized for its use of natural materials and distinctive patterns, often from the northern parts of Africa", "Explanation": "This refers to African basket weaving, so the photo should include a photo of a detailed hand-woven basket with complex patterns", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 71, "Hint": "A detailed hand-woven African basket with complex patterns."}, {"Prompt": "The traditional craft that represents Native American artistic heritage", "Explanation": "This refers to Native American pottery, so the photo should include traditional Native American clay pots with intricate designs", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 72, "Hint": "Traditional Native American clay pots with intricate designs."}, {"Prompt": "Most representative craft of Thailand", "Explanation": "This refers to Thai silk, so the photo should include a colorful silk fabric or traditional Thai garment", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 73, "Hint": "Thai silk fabric or traditional Thai garment."}, {"Prompt": "The craft that embodies Swiss precision and artistry", "Explanation": "This refers to Swiss watches, so the photo should include a detailed luxury watch", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 74, "Hint": "Detailed luxury Swiss watch."}, {"Prompt": "The traditional craft that symbolizes Chinese artistry and heritage", "Explanation": "This refers to porcelain, so the photo should include a porcelain vase or bowl", "Category": "Cultural knowledge", "Subcategory": "Craft", "prompt_id": 75, "Hint": "Porcelain vase or bowl."}, {"Prompt": "Awe-inspiring ancient Egyptian architecture, colossal and majestic under the desert sun", "Explanation": "The model should generate an image featuring the Pyramids of Giza, emphasizing the grandeur of the Great Pyramid and the surrounding desert environment", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 76, "Hint": "The Pyramids of Giza under the desert sun."}, {"Prompt": "A neo-Gothic clock tower, a symbol of London's political and cultural history", "Explanation": "The model should generate an image featuring Big Ben, highlighting its Gothic-style clock tower and the surrounding Houses of Parliament", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 77, "Hint": "Big Ben with its neo-Gothic clock tower and the surrounding Houses of Parliament."}, {"Prompt": "An ancient amphitheater, a testament to Roman engineering and entertainment", "Explanation": "The model should generate an image featuring the Colosseum, showcasing its ruined circular structure and historical significance", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 78, "Hint": "The Colosseum in its ruined circular structure, showcasing historical significance."}, {"Prompt": "An elegant marble mausoleum, a symbol of love and Indian architectural brilliance", "Explanation": "The model should generate an image featuring the Taj Mahal, showcasing its white marble exterior and symmetrical structure", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 79, "Hint": "<PERSON><PERSON>, showcasing its white marble exterior and symmetrical structure."}, {"Prompt": "A massive stone statue of a mythical creature that is a prominent historical landmark in Egypt", "Explanation": "The model should generate an image featuring the Great Sphinx of Giza, highlighting its lion's body with a human head, and its surrounding desert", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 80, "Hint": "The Great Sphinx of Giza with a lion's body and a human head, surrounded by desert."}, {"Prompt": "An impressive castle, a place of royalty and history in the Scottish city of Edinburgh", "Explanation": "The model should generate an image featuring Edinburgh Castle, showcasing its location on a hill, its ancient stone architecture, and its historic significance", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 81, "Hint": "Edinburgh Castle on a hill, featuring ancient stone architecture and showcasing its historic significance."}, {"Prompt": "The world's tallest skyscraper, a marvel of modern engineering in the desert", "Explanation": "The model should generate an image featuring the Burj <PERSON>fa, highlighting its ultra-tall skyscraper design and the surrounding Dubai cityscape", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 82, "Hint": "<PERSON><PERSON>j <PERSON> with the surrounding Dubai cityscape."}, {"Prompt": "A complex of ancient temples in Southeast Asia, a symbol of spirituality and history in Cambodia", "Explanation": "The model should generate an image featuring Angkor Wat in Cambodia, showcasing its ancient Khmer architecture and its intricate carvings", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 83, "Hint": "Angkor Wat in Cambodia, showcasing its ancient Khmer architecture and intricate carvings."}, {"Prompt": "A colossal sculpture in Brazil, with outstretched arms overlooking the city below", "Explanation": "The model should generate an image featuring the Christ the Redeemer statue, showcasing its overlooking perspective from Corcovado Mountain and its religious significance", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 84, "Hint": "Christ the Redeemer statue with outstretched arms, overlooking the city below from Corcovado Mountain."}, {"Prompt": "A remarkable church with a spherical dome, a symbol of faith, located in the Vatican City", "Explanation": "The model should generate an image featuring St. Peter's Basilica in Vatican City, emphasizing its large dome, its grand interior and its religious importance", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 85, "Hint": "St. Peter's Basilica in Vatican City, featuring its large spherical dome, grand interior, and religious significance."}, {"Prompt": "A symbol of the United States' political history, a white dome with a unique architecture in Washington DC", "Explanation": "The model should generate an image featuring the US Capitol Building in Washington DC, highlighting its white dome and its historical context", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 86, "Hint": "US Capitol Building with a white dome."}, {"Prompt": "A unique structure with blades, in the flat agricultural landscape of the Netherlands", "Explanation": "The model should generate an image featuring traditional Dutch windmills, showcasing their sails, circular structures, and the surrounding fields or canal landscapes", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 87, "Hint": "Traditional Dutch windmills with sails and circular structures, set in a flat agricultural landscape with fields or canals."}, {"Prompt": "A monument in Brazil, with arms outstretched above the city", "Explanation": "The model should generate an image featuring <PERSON> the Redeemer, emphasizing its presence in the city's skyline and its symbolic meaning", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 88, "Hint": "<PERSON> the Redeemer statue above the city skyline in Brazil."}, {"Prompt": "A magnificent gothic cathedral, an architectural marvel of arches, spires, and stained glass windows in Paris", "Explanation": "The model should generate an image featuring Notre Dame Cathedral in Paris, emphasizing its Gothic architecture, its rose window, and its historical significance", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 89, "Hint": "Notre Dame Cathedral in Paris, emphasizing its Gothic architecture, rose window, and historical significance."}, {"Prompt": "A famous medieval bridge, with arches over the river, located in the capital city of the Czech Republic, Prague", "Explanation": "The model should generate an image featuring the Charles Bridge in Prague, highlighting its medieval stone structure, the statues along the sides, and the river below", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 90, "Hint": "Charles Bridge in Prague with its medieval stone structure, statues along the sides, and the river below."}, {"Prompt": "A famous structure built in ancient Egypt as a burial place", "Explanation": "The model should generate an image of a pyramid", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 91, "Hint": "Pyramid."}, {"Prompt": "A wrought-iron lattice tower, a symbol of Parisian elegance and innovation", "Explanation": "The model should generate an image featuring the Eiffel Tower, capturing its distinctive lattice structure and the surrounding cityscape", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 92, "Hint": "Eiffel Tower with surrounding cityscape."}, {"Prompt": "China's most iconic architecture, a magnificent ancient defense line against invasion", "Explanation": "The model should generate an image featuring the Great Wall of China, showcasing its winding path and the surrounding natural landscape", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 93, "Hint": "Great Wall of China with its winding path and surrounding natural landscape."}, {"Prompt": "A structure that touches the sky, showcasing modern design and urban aspirations in the American city of New York", "Explanation": "The model should generate an image featuring the Empire State Building in New York City, emphasizing its height, its Art Deco style, and its iconic presence in the city skyline", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 94, "Hint": "Empire State Building with its Art Deco style and iconic presence in the New York City skyline."}, {"Prompt": "A soaring communication tower, dominating the Toronto skyline", "Explanation": "The model should generate an image featuring the CN Tower, showcasing its soaring modern architectural style", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 95, "Hint": "CN Tower, a soaring modern communication tower dominating the Toronto skyline."}, {"Prompt": "An iconic bridge, known for its red hue and location over a famous bay in San Francisco", "Explanation": "The model should generate an image featuring the Golden Gate Bridge in San Francisco, highlighting its red color, its towering structure, and its location over the bay", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 96, "Hint": "Golden Gate Bridge in San Francisco, with its red color and towering structure over the bay."}, {"Prompt": "A monumental stone structure at a place where ancient cultures thrived in the Andes mountains of Peru", "Explanation": "The model should generate an image featuring <PERSON><PERSON>, showcasing its location in the Andes mountains and its ancient stone architecture", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 97, "Hint": "<PERSON>hu Picchu in the Andes mountains with ancient stone architecture."}, {"Prompt": "A unique structure of circular design, symbolizing the power of the British government, located near a palace in London", "Explanation": "The model should generate an image featuring the British Museum in London, highlighting its unique circular design, and its neoclassical features", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 98, "Hint": "The British Museum in London with its unique circular design and neoclassical features."}, {"Prompt": "Ancient rock-carved churches, a spiritual pilgrimage site in Ethiopia", "Explanation": "The model should generate an image featuring the Rock-Hewn Churches of Lalibela, showcasing their unique structures carved out of rock", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 99, "Hint": "Rock-Hewn Churches of Lalibela, showcasing unique structures carved out of rock."}, {"Prompt": "An unfinished basilica, a masterpiece of Catalan Modernism with intricate facades", "Explanation": "The model should generate an image featuring the Sagrada Familia, emphasizing its unique spires and the elaborately decorated facade", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 100, "Hint": "Sagrada Familia with its unique spires and elaborately decorated facade."}, {"Prompt": "A grand opera house known for its extravagant interior, a symbol of arts in the Italian city of Milan", "Explanation": "The model should generate an image featuring the Teatro alla Scala in Milan, showcasing its opulent interior and its classical architecture", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 101, "Hint": "Teatro alla Scala in Milan, showcasing its opulent interior and classical architecture."}, {"Prompt": "A sail-like structure, an architectural icon on Sydney's harbor", "Explanation": "The model should generate an image featuring the Sydney Opera House, highlighting its distinctive white sail-like roof and the surrounding harbor view", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 102, "Hint": "Sydney Opera House with its distinctive white sail-like roof and surrounding harbor view."}, {"Prompt": "A symbol of royal power and ambition, a magnificent palace with elaborate gardens in France", "Explanation": "The model should generate an image featuring the Palace of Versailles, showcasing its grandeur, gardens, and historical significance", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 103, "Hint": "The Palace of Versailles with its magnificent architecture and elaborate gardens, showcasing grandeur and historical significance."}, {"Prompt": "A copper-clad statue, a beacon of freedom and American ideals", "Explanation": "The model should generate an image featuring the Statue of Liberty, highlighting its towering presence and iconic torch-bearing figure", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 104, "Hint": "Statue of Liberty."}, {"Prompt": "A modern structure that appears like a floating building, a beacon of light and art in the Spanish city of Bilbao", "Explanation": "The model should generate an image featuring the Guggenheim Museum Bilbao, showcasing its unique curved and metallic structure and its riverside location", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 105, "Hint": "The Guggenheim Museum Bilbao, featuring its unique curved and metallic structure by the riverside."}, {"Prompt": "A symbol of imperial China, a sprawling complex of palaces and temples in Beijing", "Explanation": "The model should generate an image featuring the Forbidden City in Beijing, showcasing its vast scale and traditional Chinese architecture", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 106, "Hint": "The Forbidden City in Beijing, showcasing its vast scale and traditional Chinese architecture."}, {"Prompt": "An opulent opera house, a center of culture and performing arts in Buenos Aires", "Explanation": "The model should generate an image featuring the Teatro Colón, showcasing its lavish interior decoration and classical architectural style", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 107, "Hint": "Teatro Colón interior with lavish decoration and classical architecture."}, {"Prompt": "A spiraling tower, a symbol of modern architecture and a landmark in the Italian city of Pisa", "Explanation": "The model should generate an image featuring the Leaning Tower of Pisa, highlighting its unique leaning structure and its historical context", "Category": "Cultural knowledge", "Subcategory": "Construction", "prompt_id": 108, "Hint": "Leaning Tower of Pisa, highlighting its unique leaning structure and historical context."}, {"Prompt": "The fruit known for its strong odor and distinctive taste", "Explanation": "The model should generate an image of a durian", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 109, "Hint": "<PERSON><PERSON>."}, {"Prompt": "The fruit known for its high water content and refreshing, sweet taste", "Explanation": "The model should generate an image of a watermelon, which is mostly water and is perfect for hydrating during summer", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 110, "Hint": "Watermelon."}, {"Prompt": "A fragrant bloom, often found in the Provence region, symbolically tied to French culture and cuisine", "Explanation": "The model should generate an image featuring lavender", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 111, "Hint": "Lavender."}, {"Prompt": "A common fruit, often associated with American pies and a symbol of fall harvest in the United States", "Explanation": "The model should generate an image featuring apples", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 112, "Hint": "Apples."}, {"Prompt": "A small, oval fruit with a fuzzy skin and a sweet, tart taste, often associated with the start of summer and the colour pink", "Explanation": "The model should generate an image related to peaches", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 113, "Hint": "Peach."}, {"Prompt": "A small fruit with a sweet and sour taste, often found in the Amazonian parts of Colombia, used in popular juice", "Explanation": "The model should generate an image featuring <PERSON><PERSON>", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 114, "Hint": "<PERSON><PERSON>."}, {"Prompt": "A resilient plant, associated with Mexican deserts and culture, known for its prickly texture", "Explanation": "The model should generate an image featuring a cactus, particularly the prickly pear", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 115, "Hint": "Cactus, particularly the prickly pear."}, {"Prompt": "An autumn delight, a fruit with cultural significance in Japan, often seen in traditional dishes and rituals", "Explanation": "The model should generate an image featuring persimmons", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 116, "Hint": "Per<PERSON><PERSON><PERSON>."}, {"Prompt": "The large green fruit known for its smooth, creamy texture, often used in savoury dishes as a substitute for meat", "Explanation": "The model should generate an image related to avocados", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 117, "Hint": "Avocado."}, {"Prompt": "The small, round, red fruit, known for its crisp texture, that is often said to keep the doctor away", "Explanation": "The model should generate an image related to apples", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 118, "Hint": "Red apple."}, {"Prompt": "A national tree of Argentina, known for its bright red flowers that bloom in the spring", "Explanation": "The model should generate an image featuring a ceibo tree", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 119, "Hint": "Ceibo tree with bright red flowers."}, {"Prompt": "The climbing plant with large, heart-shaped leaves and a distinctive, sweet scent, often associated with happiness and new beginnings", "Explanation": "The model should generate an image related to Morning Glory flowers", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 120, "Hint": "Morning Glory flowers."}, {"Prompt": "A flower that is often associated with the arrival of spring, and known for its vibrant colours and cup-like shape", "Explanation": "The model should generate an image of a tulip", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 121, "Hint": "<PERSON><PERSON>."}, {"Prompt": "A flower deeply significant in Vietnamese culture, representing purity, serenity, and spiritual growth", "Explanation": "The model should generate an image featuring a lotus flower", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 122, "Hint": "Lotus flower."}, {"Prompt": "A large yellow flower that faces the sun", "Explanation": "The model should generate an image of the sunflower", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 123, "Hint": "Sunflower."}, {"Prompt": "A fruit with a vibrant red color, often used in Korean dishes and drinks, known for its unique taste", "Explanation": "The model should generate an image featuring <PERSON><PERSON><PERSON><PERSON> (Korean raspberries)", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 124, "Hint": "<PERSON><PERSON><PERSON><PERSON> (Korean raspberries)."}, {"Prompt": "A citrus fruit known for its sweetness and juiciness, widely cultivated in Spain, especially the Valencia variety", "Explanation": "The model should generate an image featuring oranges", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 125, "Hint": "Oranges."}, {"Prompt": "A plant historically significant in ancient Egypt for making paper, associated with the Nile river", "Explanation": "The model should generate an image featuring a papyrus plant", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 126, "Hint": "Papyrus plant."}, {"Prompt": "A fruit that has a unique appearance, with segments that look like fingers, originating from a region in Southeast Asia, often used in desserts", "Explanation": "The model should generate an image featuring a Buddha's Hand fruit", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 127, "Hint": "<PERSON>'s Hand fruit."}, {"Prompt": "The fruit often used as both a food and a decorative item in festivals", "Explanation": "The model should generate an image of a pomegranate, known for its ruby-red seeds and symbolizing abundance", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 128, "Hint": "Pomegranate."}, {"Prompt": "A fruit renowned for its smooth texture and sweetness, often regarded as one of the best in the world, particularly in the Philippines", "Explanation": "The model should generate an image featuring mangoes", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 129, "Hint": "Mangoes."}, {"Prompt": "A berry-like fruit that is a vibrant dark colour, often used in jams, or to flavour other foods", "Explanation": "The model should generate an image related to blueberries", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 130, "Hint": "Blueberries."}, {"Prompt": "A tree symbolizing peace, wisdom, and the long history of Greek agriculture", "Explanation": "The model should generate an image featuring an olive tree", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 131, "Hint": "Olive tree."}, {"Prompt": "The plant that represents the beauty of Japan", "Explanation": "The model should generate an image of a cherry blossom tree, symbolizing the transient beauty of life in Japan", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 132, "Hint": "Cherry blossom tree."}, {"Prompt": "The sweet fruit with a smooth skin, a large central seed, and a bright red colour that is often associated with love and romance", "Explanation": "The model should generate an image related to cherries", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 133, "Hint": "<PERSON>."}, {"Prompt": "A small, brown, fuzzy fruit with bright green flesh, a national symbol of New Zealand", "Explanation": "The model should generate an image featuring kiwis", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 134, "Hint": "Kiwi fruit."}, {"Prompt": "A delicate blossom, a symbol of beauty and renewal in Japan, especially during the spring season", "Explanation": "The model should generate an image featuring cherry blossoms (sakura)", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 135, "Hint": "Cherry blossoms (sakura)."}, {"Prompt": "The citrus fruit often segmented, with a vibrant orange peel and a sweet, tangy taste, often enjoyed during winter", "Explanation": "The model should generate an image related to oranges", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 136, "Hint": "Orange."}, {"Prompt": "The plant known for its sharp thorns and delicate, fragrant blossoms, often associated with resilience and romance", "Explanation": "The model should generate an image of a rose bush", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 137, "Hint": "<PERSON> bush."}, {"Prompt": "A yellow, curved fruit that monkeys like", "Explanation": "The model should generate an image of a banana or bananas", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 138, "Hint": "Banana."}, {"Prompt": "A tree of deep cultural and economic significance in Italy, particularly in relation to olive oil production", "Explanation": "The model should generate an image featuring an olive tree", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 139, "Hint": "Olive tree."}, {"Prompt": "The small, sweet fruit that grows in bunches on a vine, and is often used to make wine or juice", "Explanation": "The model should generate an image related to grapes", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 140, "Hint": "Grapes."}, {"Prompt": "A tree symbolic of Russian landscapes, especially in the vast forests of Siberia", "Explanation": "The model should generate an image featuring a birch tree", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 141, "Hint": "Birch tree."}, {"Prompt": "The fruit that is typically used to make lemonade", "Explanation": "The model should generate an image of lemons", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 142, "Hint": "Lemons."}, {"Prompt": "An image of a tree with distinct dark green needle-like leaves and cones, often associated with winter and Christmas celebrations", "Explanation": "The model should generate an image of a pine tree", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 143, "Hint": "Pine tree."}, {"Prompt": "The flower that represents love in Western culture", "Explanation": "The model should generate an image of a red rose, symbolizing romance and passion", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 144, "Hint": "Red rose."}, {"Prompt": "A national flower of Colombia, celebrated for its rich diversity", "Explanation": "The model should generate an image featuring an orchid, specifically a Cattleya trianae", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 145, "Hint": "<PERSON><PERSON>a trianae orchid."}, {"Prompt": "Show a plant that is a symbol of good fortune in Irish culture, and is known for its three-lobed leaves", "Explanation": "The model should generate an image related to shamrocks", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 146, "Hint": "Shamrock."}, {"Prompt": "Show a plant that is well known for its bright red leaves in the winter season and is often used for festive decoration", "Explanation": "The model should generate an image related to the poinsettia", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 147, "Hint": "Poinsettia with bright red leaves used for festive decoration in the winter season."}, {"Prompt": "A fruit that is a traditional part of the Chinese New Year, often associated with good fortune and prosperity", "Explanation": "The model should generate an image featuring Mandarin oranges", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 148, "Hint": "Mandarin oranges."}, {"Prompt": "The plant often gifted on Mother's Day", "Explanation": "The model should generate an image of a bouquet of carnations, a popular flower symbolizing love and admiration, often given on <PERSON>'s Day", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 149, "Hint": "Bouquet of carnations."}, {"Prompt": "The tallest tree species in the world", "Explanation": "The model should generate an image of a coast redwood tree, highlighting its towering height and dense foliage", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 150, "Hint": "Coast redwood tree, highlighting its towering height and dense foliage."}, {"Prompt": "A sweet and nutritious fruit, cultivated for centuries in Egypt, holding great cultural significance", "Explanation": "The model should generate an image featuring dates", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 151, "Hint": "Dates."}, {"Prompt": "A dark purple berry from the Amazon, known for its health benefits and used in popular dishes in Brazil", "Explanation": "The model should generate an image featuring acai berries", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 152, "Hint": "Acai berries."}, {"Prompt": "The largest individual flower in the world, native to the rainforests of Indonesia", "Explanation": "The model should generate an image featuring a Rafflesia flower", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 153, "Hint": "<PERSON><PERSON>ia flower."}, {"Prompt": "The plant that most represents peace", "Explanation": "The model should generate an image of an olive branch, a universal symbol of peace", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 154, "Hint": "Olive branch."}, {"Prompt": "A unique flower, a symbol of South Africa's national flora and its biodiversity", "Explanation": "The model should generate an image featuring a protea flower", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 155, "Hint": "Protea flower."}, {"Prompt": "A flower that symbolizes purity in China", "Explanation": "This refers to the lotus, which is often associated with purity and spiritual enlightenment, growing in muddy waters yet remaining untainted", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 156, "Hint": "Lotus."}, {"Prompt": "The plant revered in ancient Egyptian culture", "Explanation": "The model should generate an image of the papyrus plant, symbolizing ancient Egyptian writing and art", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 157, "Hint": "Papyrus plant."}, {"Prompt": "A plant with delicate purple flowers that is often used to give food and drinks a particular flavour and is known for its calming qualities", "Explanation": "The model should generate an image related to lavender", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 158, "Hint": "Lavender."}, {"Prompt": "A flower with deep cultural and religious significance in India, representing purity and beauty", "Explanation": "The model should generate an image featuring a lotus flower", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 159, "Hint": "Lotus flower."}, {"Prompt": "The tree that represents strength in many cultures", "Explanation": "The model should generate an image of an oak tree, known for its strength and longevity", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 160, "Hint": "Oak tree."}, {"Prompt": "A distinctive fruit in Thailand, often referred to as the 'king of fruits' in Southeast Asia, known for its strong odor", "Explanation": "The model should generate an image featuring durians", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 161, "Hint": "Durians."}, {"Prompt": "A large, broad-leafed tropical plant, often associated with hospitality and used in a variety of dishes", "Explanation": "The model should generate an image of a banana plant", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 162, "Hint": "Banana plant."}, {"Prompt": "The bright yellow citrus fruit, known for its sour taste and high acidity, that is often used in cooking and baking", "Explanation": "The model should generate an image related to lemons", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 163, "Hint": "Lemon."}, {"Prompt": "A prickly green plant found in deserts", "Explanation": "The model should generate an image of the cactus", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 164, "Hint": "Cactus."}, {"Prompt": "The most drought-resistant plant in the desert", "Explanation": "The model should generate an image of a cactus, emphasizing its ability to store water and survive in arid conditions", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 165, "Hint": "Cactus."}, {"Prompt": "The fruit that most associates with Christmas", "Explanation": "The model should generate an image of a cranberry, a fruit widely used in holiday meals and decorations", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 166, "Hint": "Cranberry."}, {"Prompt": "A national plant symbol of New Zealand, often seen in sports and cultural contexts", "Explanation": "The model should generate an image featuring a silver fern", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 167, "Hint": "Silver fern."}, {"Prompt": "A bright yellow fruit, considered the national fruit of Jamaica and can be eaten raw or cooked", "Explanation": "The model should generate an image featuring <PERSON><PERSON><PERSON>", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 168, "Hint": "<PERSON><PERSON><PERSON>."}, {"Prompt": "A type of fruit that is often used for both sweet and savoury dishes, known for its mild and adaptable flavour", "Explanation": "The model should generate an image related to pears", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 169, "Hint": "<PERSON><PERSON>."}, {"Prompt": "A fruit known for its unique shape, with a star-like cross section when sliced, a popular snack in Southeast Asia", "Explanation": "The model should generate an image featuring Star Fruit (Carambola)", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 170, "Hint": "Star Fruit (Carambola)."}, {"Prompt": "An iconic tree in African savannas, symbolizing national parks and wildlife in Kenya", "Explanation": "The model should generate an image featuring an acacia tree", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 171, "Hint": "Acacia tree."}, {"Prompt": "A beloved fruit in India, known as the 'king of fruits,' cherished for its rich flavor and cultural significance", "Explanation": "The model should generate an image featuring mangoes", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 172, "Hint": "Mangoes."}, {"Prompt": "The tropical fruit with a spiky exterior and sweet, juicy interior", "Explanation": "The model should generate an image of a pineapple, a tropical fruit with a rough, spiky skin and sweet, tangy flesh", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 173, "Hint": "Pineapple."}, {"Prompt": "The fruit known for its smooth, waxy skin, pear-like shape, and sweet taste, that is often found in tropical regions", "Explanation": "The model should generate an image related to mangoes", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 174, "Hint": "Mango."}, {"Prompt": "A famous flower that symbolizes wealth in China", "Explanation": "This refers to the peony, often called the 'King of Flowers' in China, symbolizing wealth, prosperity, and good fortune in Chinese culture", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 175, "Hint": "Peony."}, {"Prompt": "A famous flower that symbolizes loyalty in China", "Explanation": "This refers to the chrysanthemum, which symbolizes loyalty, endurance, and integrity in Chinese culture, often associated with the late autumn season", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 176, "Hint": "Chrysanthemum."}, {"Prompt": "A tropical fruit, enjoyed fresh or in candies in Mexico, characterized by its distinctive flavor and often paired with chili", "Explanation": "The model should generate an image featuring mangoes", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 177, "Hint": "Mangoes."}, {"Prompt": "Show an image of a plant whose leaves are used in many dishes in the Mediterranean and is known for its aromatic scent and needle-like leaves", "Explanation": "The model should generate an image related to rosemary", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 178, "Hint": "Rosemary plant with needle-like leaves."}, {"Prompt": "A tall, sturdy plant with large heads of grain, often associated with farming and the harvest season", "Explanation": "The model should generate an image related to wheat", "Category": "Cultural knowledge", "Subcategory": "Plant", "prompt_id": 179, "Hint": "Wheat."}, {"Prompt": "A majestic striped animal, symbolizing the wildlife of Bangladesh", "Explanation": "The model should generate an image featuring a Royal Bengal tiger", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 180, "Hint": "Royal Bengal tiger."}, {"Prompt": "The large bird known for its powerful talons and hunting prowess, often seen soaring in the mountains or open skies", "Explanation": "The model should generate an image of an eagle", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 181, "Hint": "Eagle."}, {"Prompt": "Show an image of a small primate with large, expressive eyes, often known for their nocturnal habits", "Explanation": "The model should generate an image of a tarsier", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 182, "Hint": "Tarsier."}, {"Prompt": "An animal with a long nose", "Explanation": "The model should generate an image of an elephant", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 183, "Hint": "Elephant."}, {"Prompt": "The fastest land animal", "Explanation": "The model should generate an image of a cheetah, known for its incredible speed on land", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 184, "Hint": "Cheetah."}, {"Prompt": "A large animal, a symbol of national pride in Thailand", "Explanation": "The model should generate an image featuring an elephant", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 185, "Hint": "Elephant."}, {"Prompt": "A majestic striped animal, a symbol of India's wildlife", "Explanation": "The model should generate an image featuring a Bengal tiger", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 186, "Hint": "Bengal tiger."}, {"Prompt": "An animal representing natural heritage in Italy", "Explanation": "The model should generate an image featuring an Italian wolf", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 187, "Hint": "Italian wolf."}, {"Prompt": "A feline figure on the Dutch coat of arms", "Explanation": "The model should generate an image featuring a lion", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 188, "Hint": "Lion."}, {"Prompt": "The animal known for its distinctive hump and ability to survive in deserts", "Explanation": "The model should generate an image of a camel, characterized by its hump and desert adaptability", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 189, "Hint": "Camel."}, {"Prompt": "The flying animal associated with wisdom and long life in Asian culture", "Explanation": "The model should generate an image of a crane, symbolizing wisdom and longevity", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 190, "Hint": "<PERSON>."}, {"Prompt": "The creature that crawls slowly with a protective shell on its back", "Explanation": "The model should generate an image of a turtle or tortoise", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 191, "Hint": "Turtle or tortoise."}, {"Prompt": "The first element in the Chinese Five Elements system", "Explanation": "The model should generate an image of wood", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 192, "Hint": "Wood."}, {"Prompt": "A powerful marsupial, a national symbol of Australia", "Explanation": "The model should generate an image featuring a kangaroo", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 193, "Hint": "Kangaroo."}, {"Prompt": "A mythical creature embodying Singapore's origins", "Explanation": "The model should generate an image featuring a merlion", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 194, "Hint": "Merlion."}, {"Prompt": "The large feline with a golden coat and black spots, often seen as a symbol of grace and beauty in the wild", "Explanation": "The model should generate an image of a leopard", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 195, "Hint": "<PERSON><PERSON>."}, {"Prompt": "The animal that is a symbol of good luck in Chinese culture", "Explanation": "The model should generate an image of a koi fish, representing prosperity and perseverance", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 196, "Hint": "Koi fish."}, {"Prompt": "The animal that emerges from a cocoon, symbolizing transformation", "Explanation": "The model should generate an image of a butterfly emerging from a cocoon, highlighting the process of metamorphosis", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 197, "Hint": "Butterfly emerging from a cocoon, symbolizing transformation."}, {"Prompt": "The aquatic mammal known for its playful nature and intelligence, often seen performing tricks in marine parks", "Explanation": "The model should generate an image of a dolphin", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 198, "Hint": "Dolphin."}, {"Prompt": "The largest animal in the ocean", "Explanation": "The model should generate an image of a blue whale, the largest animal inhabiting the ocean", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 199, "Hint": "Blue whale."}, {"Prompt": "The bird famous for its colorful feathers and mimicry of sounds", "Explanation": "The model should generate an image of a parrot, renowned for its vibrant colors and ability to mimic human speech", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 200, "Hint": "A parrot with vibrant colors and known for mimicking sounds."}, {"Prompt": "A representation of natural heritage in Italy", "Explanation": "The model should generate an image featuring an Italian wolf", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 201, "Hint": "Italian wolf."}, {"Prompt": "An animal featured on the United Kingdom's coat of arms", "Explanation": "The model should generate an image featuring a lion", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 202, "Hint": "Lion."}, {"Prompt": "The animal known for its sly and cunning nature", "Explanation": "The model should generate an image of a fox, often portrayed as clever and cunning in folklore", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 203, "Hint": "Fox."}, {"Prompt": "The animal symbolizing purity and sacrifice in Christian teachings", "Explanation": "The model should generate an image of a lamb", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 204, "Hint": "<PERSON>."}, {"Prompt": "An iconic reptile, a symbol of biodiversity in Indonesia", "Explanation": "The model should generate an image featuring a Komodo dragon", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 205, "Hint": "Komodo dragon."}, {"Prompt": "A revered animal, with cultural significance in Nepal", "Explanation": "The model should generate an image featuring a cow", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 206, "Hint": "Cow."}, {"Prompt": "The animal known for its long neck and ability to reach tall trees", "Explanation": "The model should generate an image of a giraffe, recognized by its long neck and height", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 207, "Hint": "Giraffe."}, {"Prompt": "The animal associated with wisdom and guidance in Greek mythology", "Explanation": "The model should generate an image of an owl, symbolizing wisdom and often seen as <PERSON>'s companion", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 208, "Hint": "An owl, symbolizing wisdom and often seen as <PERSON>'s companion in Greek mythology."}, {"Prompt": "An animal, a symbol of strength in Brazil", "Explanation": "The model should generate an image featuring a jaguar", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 209, "Hint": "Jaguar."}, {"Prompt": "The animal that crows in the morning", "Explanation": "The model should generate an image of a rooster, the animal known for its morning crowing", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 210, "Hint": "Rooster."}, {"Prompt": "The smallest bird in the world", "Explanation": "The model should generate an image of a hummingbird", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 211, "Hint": "Hummingbird."}, {"Prompt": "The animal that symbolizes rebirth and immortality in mythology", "Explanation": "The model should generate an image of a phoenix, rising from flames to symbolize renewal and eternal life", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 212, "Hint": "Phoenix rising from flames, symbolizing renewal and eternal life."}, {"Prompt": "A national treasure animal of China", "Explanation": "The model should generate an image featuring a giant panda", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 213, "Hint": "Giant panda."}, {"Prompt": "A horned animal, culturally significant in Spain", "Explanation": "The model should generate an image featuring a bull", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 214, "Hint": "Bull."}, {"Prompt": "A bird of prey, a national symbol of the United States", "Explanation": "The model should generate an image featuring a bald eagle", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 215, "Hint": "Bald eagle."}, {"Prompt": "A vital beast of burden in Philippine agriculture", "Explanation": "The model should generate an image featuring a carabao", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 216, "Hint": "Carabao."}, {"Prompt": "A breed known for rescue in the Swiss Alps", "Explanation": "The model should generate an image featuring a St Bernard dog", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 217, "Hint": "St Bernard dog."}, {"Prompt": "The animal often depicted as the protector of treasures in East Asian mythology", "Explanation": "The model should generate an image of a dragon, symbolizing power, protection, and mystery in East Asian culture", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 218, "Hint": "Dragon."}, {"Prompt": "The national animal of South Africa", "Explanation": "The model should generate an image featuring a springbok", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 219, "Hint": "Springbok."}, {"Prompt": "The animal symbolizing hard work and dedication in Chinese culture", "Explanation": "The model should generate an image of a cow", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 220, "Hint": "Cow."}, {"Prompt": "A South American pack animal of the Andes", "Explanation": "The model should generate an image featuring a llama", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 221, "Hint": "Llama."}, {"Prompt": "The largest land animal in the world", "Explanation": "The model should generate an image of an African elephant", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 222, "Hint": "African elephant."}, {"Prompt": "An animal with long horns, a symbol of conservation in the UAE", "Explanation": "The model should generate an image featuring an Arabian oryx", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 223, "Hint": "Arabian oryx."}, {"Prompt": "A bird of prey, a national symbol of Mexico", "Explanation": "The model should generate an image featuring a golden eagle", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 224, "Hint": "Golden eagle."}, {"Prompt": "A fluffy, tree-dwelling marsupial native to Australia", "Explanation": "The model should generate an image of a koala", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 225, "Hint": "Koala."}, {"Prompt": "A small, brightly coloured bird known for its ability to hover in the air while drinking nectar from flowers", "Explanation": "The model should generate an image of a hummingbird", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 226, "Hint": "Hummingbird."}, {"Prompt": "The large, herbivorous mammal known for its stripes and preference for grasslands, common in African wildlife", "Explanation": "The model should generate an image of a zebra", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 227, "Hint": "Zebra"}, {"Prompt": "The animal that is often associated with loyalty and companionship", "Explanation": "The model should generate an image of a dog, symbolizing loyalty and companionship", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 228, "Hint": "Dog."}, {"Prompt": "An animal central to farming and culture in Vietnam", "Explanation": "The model should generate an image featuring a water buffalo", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 229, "Hint": "Water buffalo."}, {"Prompt": "A primate with a distinctive red face, living in northern Japan", "Explanation": "The model should generate an image featuring a Japanese macaque", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 230, "Hint": "Japanese macaque."}, {"Prompt": "The animal known for its majestic antlers and serene presence", "Explanation": "The model should generate an image of a deer, often associated with grace and gentleness", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 231, "Hint": "Deer."}, {"Prompt": "The animal that symbolizes peace and purity", "Explanation": "The model should generate an image of a dove, often associated with peace and spiritual purity", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 232, "Hint": "Dove."}, {"Prompt": "Show an image of a large, grey animal with thick skin and a horn on its nose, often found in Africa and Asia", "Explanation": "The model should generate an image of a rhinoceros", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 233, "Hint": "Rhinoceros."}, {"Prompt": "Show a large animal that lives in the arctic and subarctic, known for its thick white fur", "Explanation": "The model should generate an image of a polar bear", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 234, "Hint": "Polar bear."}, {"Prompt": "An endangered striped animal, a conservation symbol in Malaysia", "Explanation": "The model should generate an image featuring a Malayan tiger", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 235, "Hint": "Malayan tiger."}, {"Prompt": "A semi-aquatic rodent, a national symbol of Canada", "Explanation": "The model should generate an image featuring a beaver", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 236, "Hint": "Beaver."}, {"Prompt": "The animal that symbolizes freedom and power in the sky", "Explanation": "The model should generate an image of an eagle, representing freedom, vision, and strength", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 237, "Hint": "Eagle."}, {"Prompt": "A flightless bird native to New Zealand, a national symbol", "Explanation": "The model should generate an image featuring a kiwi bird", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 238, "Hint": "<PERSON><PERSON> bird."}, {"Prompt": "A large animal, a symbol of Russia's wilderness", "Explanation": "The model should generate an image featuring a Russian brown bear", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 239, "Hint": "Russian brown bear."}, {"Prompt": "A large bird, representing freedom in Colombia", "Explanation": "The model should generate an image featuring an Andean condor", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 240, "Hint": "Andean condor."}, {"Prompt": "The nocturnal mammal with a distinctive 'mask' around its eyes, often known for its mischievous behaviour", "Explanation": "The model should generate an image of a raccoon", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 241, "Hint": "<PERSON><PERSON><PERSON>."}, {"Prompt": "The animal that produces honey", "Explanation": "The model should generate an image of a honeybee, known for producing honey and living in hives", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 242, "Hint": "<PERSON><PERSON>."}, {"Prompt": "A bird embodying French heritage", "Explanation": "The model should generate an image featuring a Gallic rooster", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 243, "Hint": "Gallic rooster."}, {"Prompt": "The bird that cannot fly but is known for its speed on land", "Explanation": "The model should generate an image of an ostrich, the flightless bird famous for its running speed", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 244, "Hint": "<PERSON><PERSON><PERSON>."}, {"Prompt": "The animal that is considered sacred in Indian culture", "Explanation": "The model should generate an image of a cow, revered as a symbol of life and prosperity in India", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 245, "Hint": "Cow."}, {"Prompt": "The animal that spins webs", "Explanation": "The model should generate an image of a spider, known for its web-spinning ability", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 246, "Hint": "<PERSON>."}, {"Prompt": "An animal, embodying the Finnish wilderness", "Explanation": "The model should generate an image featuring a brown bear", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 247, "Hint": "Brown bear."}, {"Prompt": "The animal known for its black and white stripes", "Explanation": "The model should generate an image of a zebra, famous for its distinctive black and white striped pattern", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 248, "Hint": "Zebra."}, {"Prompt": "A powerful big cat native to Argentina", "Explanation": "The model should generate an image featuring a jaguar", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 249, "Hint": "Jaguar."}, {"Prompt": "A bird of prey, an emblem of Germany", "Explanation": "The model should generate an image featuring a federal eagle", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 250, "Hint": "Federal eagle."}, {"Prompt": "The only mammal capable of true flight", "Explanation": "The model should generate an image of a bat, the only mammal that can achieve sustained flight", "Category": "Cultural knowledge", "Subcategory": "Animal", "prompt_id": 251, "Hint": "Bat."}, {"Prompt": "A famous Art with a large body and strings, played by plucking", "Explanation": "The model should generate an image of a harp", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 252, "Hint": "Harp."}, {"Prompt": "An image showcasing a brass instrument, known for its coiled shape and mellow sound, common in jazz and classical settings", "Explanation": "The model should generate an image related to a French horn", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 253, "Hint": "French horn."}, {"Prompt": "An image of a wind instrument, often made of metal, that produces a bright and piercing sound, common in both classical and marching band settings", "Explanation": "The model should generate an image related to a trumpet", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 254, "Hint": "Trumpet."}, {"Prompt": "A stringed instrument with a long neck and a round body, often plucked and used in traditional Middle Eastern and North African music", "Explanation": "The model should generate an image related to an Oud", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 255, "Hint": "Oud."}, {"Prompt": "Show a set of flat, metallic plates that produce a bright, shimmering sound when struck, often used in orchestral music", "Explanation": "The model should generate an image related to cymbals", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 256, "Hint": "Cymbals."}, {"Prompt": "A string instrument of African heritage, often part of the rich heritage in West Africa", "Explanation": "The model should generate an image of the Kora", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 257, "Hint": "<PERSON><PERSON>."}, {"Prompt": "A triangular stringed instrument, often used in Russian folk music", "Explanation": "The model should generate an image of the Balalaika", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 258, "Hint": "Balalaika."}, {"Prompt": "A small, lute-like instrument with a distinctive sound, often used in Andean music of Peru", "Explanation": "The model should generate an image of the Charango", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 259, "Hint": "Charango."}, {"Prompt": "A plucked stringed instrument, often used in Hindustani classical music, known for its long neck and numerous strings", "Explanation": "The model should generate an image of the Sitar", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 260, "Hint": "Sitar."}, {"Prompt": "A long-necked stringed instrument with a rounded body, characteristic of Greek folk music", "Explanation": "The model should generate an image of the Bouzouki", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 261, "Hint": "<PERSON><PERSON><PERSON><PERSON>."}, {"Prompt": "Show an image of a percussion instrument, made from a stretched skin, that produces a deep, resonant sound when struck, used in many forms of music from all over the world", "Explanation": "The model should generate an image related to a drum", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 262, "Hint": "Drum."}, {"Prompt": "A long, wooden wind instrument, used by Aboriginal Australians for traditional music and storytelling", "Explanation": "The model should generate an image of the Didgeridoo", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 263, "Hint": "Didgeridoo."}, {"Prompt": "A pear-shaped stringed instrument, often used in classical and folk music of Egypt", "Explanation": "The model should generate an image of the Oud", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 264, "Hint": "Oud."}, {"Prompt": "A percussive instrument, characterized by its unique sound and presence in Brazilian cultural events", "Explanation": "The model should generate an image of the berimbau", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 265, "Hint": "Berimbau."}, {"Prompt": "An image of a small, handheld stringed instrument, often plucked with fingers, known for its cheerful and bright sound, commonly used in folk and country music", "Explanation": "The model should generate an image related to a banjo", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 266, "Hint": "<PERSON><PERSON>."}, {"Prompt": "A stringed instrument, central to flamenco music, known for its intricate fretwork in Spain", "Explanation": "The model should generate an image of the Spanish Guitar", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 267, "Hint": "Spanish Guitar."}, {"Prompt": "A long, thin wind instrument made of wood, known for its delicate and haunting tones and often used to make melodies", "Explanation": "The model should generate an image related to a flute", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 268, "Hint": "Flute."}, {"Prompt": "A wind instrument, a symbol of Irish traditional music, known for its complex bellows and pipes", "Explanation": "The model should generate an image of the Uilleann Pipes", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 269, "Hint": "Uilleann Pipes."}, {"Prompt": "Depict an instrument with black and white keys, played by pressing them down, producing a wide range of sounds from delicate melodies to powerful chords", "Explanation": "The model should generate an image related to a piano", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 270, "Hint": "Piano."}, {"Prompt": "A traditional instrument of China, known for its numerous strings and bridges", "Explanation": "The model should generate an image of the Guzheng", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 271, "Hint": "Guzheng."}, {"Prompt": "A distinct percussive instrument, that is emblematic of Caribbean music", "Explanation": "The model should generate an image of the Steelpan", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 272, "Hint": "Steelpan."}, {"Prompt": "A wind instrument, that is an iconic part of traditional music of the Andes region", "Explanation": "The model should generate an image of a pan flute", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 273, "Hint": "Pan flute."}, {"Prompt": "An image of a wooden instrument that produces sound by the player blowing across a mouthpiece, known for its warm and soulful tone", "Explanation": "The model should generate an image related to a saxophone", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 274, "Hint": "Saxophone."}, {"Prompt": "A bowed string instrument, popular in Korean music, with a unique sound profile and often played in traditional performances", "Explanation": "The model should generate an image of a Haegeum", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 275, "Hint": "Haegeum."}, {"Prompt": "Show an image of a large stringed instrument, often played with a bow, known for its rich, deep tones and central role in the orchestra", "Explanation": "The model should generate an image related to a cello", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 276, "Hint": "Cello."}, {"Prompt": "A stringed instrument, commonly played in a particular form of traditional music from Argentina", "Explanation": "The model should generate an image of the Argentine guitar", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 277, "Hint": "Argentine guitar."}, {"Prompt": "A wind instrument, recognized as a prominent part of Scottish cultural tradition", "Explanation": "The model should generate an image of bagpipes", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 278, "Hint": "Bagpipes."}, {"Prompt": "A portrait in the style of <PERSON>", "Explanation": "The model should generate an image of a portrait in the style of <PERSON>, using sfumato technique, subtle lighting, realistic anatomy, and a sense of mystery and psychological depth. It should evoke the feeling of a classic <PERSON> portrait", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 279, "Hint": "Portrait using sfumato technique, subtle lighting, realistic anatomy, and a sense of mystery, resembling a classic Leonardo da Vinci style."}, {"Prompt": "A religious scene in the style of <PERSON>", "Explanation": "The model should generate an image of a religious scene in the style of <PERSON>, featuring balanced composition, harmonious forms, soft colors, and a sense of idealized beauty. It should evoke <PERSON>'s graceful and serene style", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 280, "Hint": "A religious scene with balanced composition, harmonious forms, soft colors, and idealized beauty in <PERSON>'s graceful and serene style."}, {"Prompt": "A dreamlike scene in the style of Salvador Dalí", "Explanation": "The model should generate an image of a dreamlike scene in the style of Salvador Dalí, featuring illogical juxtapositions, bizarre objects, and a sense of mystery and the subconscious, similar to <PERSON><PERSON>'s surrealist artworks", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 281, "Hint": "A dreamlike scene with illogical juxtapositions, bizarre objects, and a sense of mystery in Salvador Dalí's surrealist style."}, {"Prompt": "A peasant scene in the style of early Van Gogh", "Explanation": "The model should generate an image of a peasant scene in the style of early <PERSON> Gogh, using darker colors, coarse brushstrokes, and a focus on the everyday life of working people. It should convey a sense of rustic realism similar to paintings by <PERSON> from his early period", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 282, "Hint": "A peasant scene with darker colors, coarse brushstrokes, depicting everyday life, in the style of early Van Gogh."}, {"Prompt": "A poor man in the style of <PERSON>'s blue period", "Explanation": "The model should generate an image of a poor man in the style of <PERSON>'s Blue Period, using various shades of blue, elongated features and expressing sorrow or poverty. It should evoke sadness similar to <PERSON>'s blue period", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 283, "Hint": "A poor man in shades of blue with elongated features, expressing sorrow, in the style of <PERSON>'s Blue Period."}, {"Prompt": "An abstract painting in the style of <PERSON>", "Explanation": "The model should generate an image of an abstract painting in the style of <PERSON>, with expressive brushstrokes, non-geometric forms, dripping paint, dynamic composition", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 284, "Hint": "An abstract painting with expressive brushstrokes, non-geometric forms, dripping paint, and dynamic composition in the style of <PERSON>."}, {"Prompt": "A classical figure in the style of <PERSON><PERSON>", "Explanation": "The model should generate an image of a classical figure in the style of <PERSON><PERSON>, with muscular anatomy, dynamic poses, and a powerful and sculptural quality. It should evoke the grandeur and expressive power of <PERSON><PERSON>'s figures", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 285, "Hint": "A classical figure with muscular anatomy, dynamic pose, and sculptural quality, evoking the grandeur of <PERSON><PERSON>'s art."}, {"Prompt": "A portrait in the style of <PERSON>'s <PERSON><PERSON><PERSON>", "Explanation": "The model should generate an image of a portrait in the style of Cubism, showing fragmented and geometric shapes, multiple perspectives of the subject, and a limited color palette. It should clearly show the influence of <PERSON>'s Cubist style", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 286, "Hint": "A portrait in the style of Cubism, featuring fragmented and geometric shapes, multiple perspectives of the subject, and a limited color palette inspired by <PERSON>'s work."}, {"Prompt": "A landscape in the style of <PERSON>", "Explanation": "The model should generate an image of a landscape in the style of <PERSON>, emphasizing light and color with short, broken brushstrokes, capturing a fleeting moment in nature. The scene should reflect the impressionistic style of <PERSON><PERSON>'s", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 287, "Hint": "A landscape with short, broken brushstrokes emphasizing light and color, capturing a fleeting moment in nature in the impressionistic style of <PERSON>."}, {"Prompt": "A landscape in the style of <PERSON><PERSON>", "Explanation": "The model should generate an image of a black and white landscape in the style of <PERSON><PERSON>, with high contrast, sharp details, and an emphasis on capturing the beauty of nature, similar to <PERSON><PERSON>'s landscape photography", "Category": "Cultural knowledge", "Subcategory": "Art", "prompt_id": 288, "Hint": "A black and white landscape with high contrast, sharp details, and an emphasis on the beauty of nature, in the style of <PERSON><PERSON>'s photography."}, {"Prompt": "<PERSON>'s iconic moment", "Explanation": "The model should generate an image of <PERSON> playing soccer", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 289, "Hint": "<PERSON> playing soccer."}, {"Prompt": "<PERSON>'s iconic moment", "Explanation": "The model should generate an image of <PERSON> playing basketball", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 290, "Hint": "<PERSON> playing basketball."}, {"Prompt": "The powerful weapon wielded by <PERSON> in Marvel", "Explanation": "The model should generate an image of <PERSON>'s hammer, <PERSON><PERSON><PERSON><PERSON>", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 291, "Hint": "<PERSON>'s hammer, <PERSON><PERSON><PERSON><PERSON>."}, {"Prompt": "<PERSON><PERSON>'s weapon in Chinese mythology", "Explanation": "The model should generate an image of the bow and arrows", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 292, "Hint": "Bow and arrows."}, {"Prompt": "The weapon of  King <PERSON> in Arthurian legend", "Explanation": "The model should generate an image of <PERSON><PERSON><PERSON><PERSON>, King <PERSON>'s magical sword", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 293, "Hint": "Excalibur, King <PERSON>'s magical sword."}, {"Prompt": "<PERSON> brothers' greatest invention", "Explanation": "The model should generate an image of the airplane", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 294, "Hint": "Airplane."}, {"Prompt": "<PERSON>'s iconic moment", "Explanation": "The model should generate an image of <PERSON> performing on stage, with his signature microphone stand and dynamic stage presence", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 295, "Hint": "<PERSON> performing on stage with his signature microphone stand and dynamic stage presence."}, {"Prompt": "<PERSON>'s iconic moment", "Explanation": "The model should generate an image of <PERSON> in the boxing ring", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 296, "Hint": "<PERSON> in the boxing ring."}, {"Prompt": "<PERSON>'s iconic moment", "Explanation": "The model should generate an image of <PERSON> in a martial arts pose, showcasing his agility and strength", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 297, "Hint": "<PERSON> in a martial arts pose showcasing his agility and strength."}, {"Prompt": "The weapon of Guan Yu in the Romance of the Three Kingdoms", "Explanation": "The model should generate an image of legendary Green Dragon Crescent Blade, a large halberd with a curved blade", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 298, "Hint": "Green Dragon Crescent Blade."}, {"Prompt": "<PERSON><PERSON><PERSON>' iconic moment", "Explanation": "The model should generate an image of <PERSON><PERSON><PERSON> playing basketball", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 299, "Hint": "<PERSON><PERSON><PERSON> playing basketball."}, {"Prompt": "Thomas <PERSON>'s greatest invention", "Explanation": "The model should generate an image of the light bulb", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 300, "Hint": "Light bulb."}, {"Prompt": "<PERSON>'s iconic moment", "Explanation": "The model should generate an image of <PERSON> playing basketball", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 301, "Hint": "<PERSON> playing basketball."}, {"Prompt": "Show an image of a silent film icon, embodying a lovable, downtrodden character", "Explanation": "The model should generate an image of <PERSON> in his Tramp character", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 302, "Hint": "<PERSON> as his <PERSON><PERSON><PERSON> character."}, {"Prompt": "The weapon of Sun Wukong from Journey to the West", "Explanation": "The model should generate an image of the golden staff, also known as <PERSON><PERSON><PERSON>", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 303, "Hint": "Golden staff <PERSON><PERSON><PERSON>."}, {"Prompt": "The weapon of Artemis in Greek mythology", "Explanation": "The model should generate an image of a bow and arrows", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 304, "Hint": "Bow and arrows."}, {"Prompt": "<PERSON>'s favorite activity", "Explanation": "The model should generate an image related to music, reflecting <PERSON>'s deep connection with classical music", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 305, "Hint": "Classical music."}, {"Prompt": "<PERSON><PERSON><PERSON>'s iconic moment", "Explanation": "The model should generate an image of <PERSON><PERSON><PERSON> playing football", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 306, "Hint": "<PERSON><PERSON><PERSON> playing football."}, {"Prompt": "<PERSON>'s iconic moment", "Explanation": "The model should generate an image of <PERSON> performing his moonwalk dance", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 307, "Hint": "<PERSON> performing his moonwalk dance."}, {"Prompt": "<PERSON>'s most famous art", "Explanation": "The model should generate an image of a peaceful pond with floating lilies, using soft, blended brushstrokes typical of <PERSON><PERSON>'s Water Lilies series", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 308, "Hint": "A peaceful pond with floating lilies, using soft, blended brushstrokes typical of <PERSON><PERSON>'s Water Lilies series."}, {"Prompt": "The image of <PERSON><PERSON><PERSON> from Greek mythology", "Explanation": "The model should generate an image of <PERSON><PERSON><PERSON>, with her hair made of living snakes", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 309, "Hint": "Medusa with her hair made of living snakes."}, {"Prompt": "The food most loved by <PERSON><PERSON> from the movie franchise", "Explanation": "The model should generate an image of a banana", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 310, "Hint": "Banana."}, {"Prompt": "The object used by <PERSON> to fly in the Quidditch game", "Explanation": "The model should generate an image of a broomstick", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 311, "Hint": "Broomstick."}, {"Prompt": "The steed of Tang Sanzang from Journey to the West", "Explanation": "The model should generate an image of the white horse", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 312, "Hint": "White horse."}, {"Prompt": "The weapon of Shiva in Hindu mythology", "Explanation": "The model should generate an image of <PERSON>'s trident", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 313, "Hint": "<PERSON>'s trident."}, {"Prompt": "The magical item of Her<PERSON> in Greek mythology", "Explanation": "The model should generate an image of winged sandals", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 314, "Hint": "Winged sandals."}, {"Prompt": "<PERSON>'s favorite animal", "Explanation": "The model should generate an image of a horse", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 315, "Hint": "Horse."}, {"Prompt": "The wise master from the Kung Fu Panda movie", "Explanation": "The model should generate an image of <PERSON>, the wise tortoise", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 316, "Hint": "Master <PERSON><PERSON>, the wise tortoise from Kung Fu Panda."}, {"Prompt": "<PERSON>'s favorite flower", "Explanation": "The model should generate an image of sunflowers", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 317, "Hint": "Sunflowers."}, {"Prompt": "<PERSON>'s favorite instrument", "Explanation": "The model should generate an image of a grand piano", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 318, "Hint": "Grand piano."}, {"Prompt": "<PERSON><PERSON>'s famous experiment scene", "Explanation": "The model should generate an image of <PERSON><PERSON>'s pea plant experiment, focusing on pea plants", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 319, "Hint": "<PERSON><PERSON>'s pea plant experiment scene with pea plants."}, {"Prompt": "<PERSON>'s iconic moment", "Explanation": "The model should generate an image of <PERSON> Bryant playing basketball", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 320, "Hint": "<PERSON> playing basketball."}, {"Prompt": "<PERSON>'s iconic moment", "Explanation": "The model should generate an image of <PERSON> playing tennis", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 321, "Hint": "<PERSON> playing tennis."}, {"Prompt": "<PERSON>'s most famous play", "Explanation": "The model should generate an image of a scene from the play 'Hamlet'", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 322, "Hint": "Hamlet."}, {"Prompt": "The most iconic architectural structure of ancient Egypt", "Explanation": "The model should generate an image of the Pyramids of Giza", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 323, "Hint": "Pyramids of Giza."}, {"Prompt": "The weapon of Apollo in Greek mythology", "Explanation": "The model should generate an image of <PERSON>'s golden bow and arrows", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 324, "Hint": "<PERSON>'s golden bow and arrows."}, {"Prompt": "The weapon of Thor in Norse mythology", "Explanation": "The model should generate an image of <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>'s hammer, a powerful weapon associated with thunder and lightning", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 325, "Hint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>'s hammer, a powerful weapon associated with thunder and lightning."}, {"Prompt": "The steed of <PERSON><PERSON> in Norse mythology", "Explanation": "The model should generate an image of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>'s eight-legged horse", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 326, "Hint": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>'s eight-legged horse."}, {"Prompt": "The most famous character in Pokémon", "Explanation": "The model should generate an image of <PERSON><PERSON><PERSON>", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 327, "Hint": "<PERSON><PERSON><PERSON>."}, {"Prompt": "The face of <PERSON> from Chinese mythology", "Explanation": "The model should generate an image of <PERSON>, featuring his iconic third eye on the forehead", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 328, "Hint": "<PERSON> with an iconic third eye on his forehead."}, {"Prompt": "<PERSON>'s favorite musical instruments", "Explanation": "The model should generate an image of the violin", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 329, "Hint": "Violin."}, {"Prompt": "The animal often seen with <PERSON><PERSON><PERSON><PERSON>", "Explanation": "The model should generate an image of a donkey", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 330, "Hint": "<PERSON><PERSON>."}, {"Prompt": "The object used to find the heroine in Cinderella", "Explanation": "The model should generate an image of a glass slipper", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 331, "Hint": "Glass slipper."}, {"Prompt": "The item used by <PERSON><PERSON><PERSON> to mend the sky in Chinese mythology", "Explanation": "The model should generate an image of colorful stones or rocks", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 332, "Hint": "Colorful stones or rocks."}, {"Prompt": "<PERSON>'s art", "Explanation": "The model should generate an image of abstract art", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 333, "Hint": "Abstract art."}, {"Prompt": "The symbol of <PERSON><PERSON> in Greek mythology", "Explanation": "The model should generate an image of <PERSON><PERSON>'s golden crown", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 334, "Hint": "<PERSON><PERSON>'s golden crown."}, {"Prompt": "An iconic pose from a famous philosopher, often depicted with a hand resting on his chin, and a thoughtful expression on his face", "Explanation": "The model should generate an image related to <PERSON><PERSON>'s The Thinker", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 335, "Hint": "<PERSON><PERSON>'s The Thinker."}, {"Prompt": "The weapon of <PERSON> from Journey to the West", "Explanation": "The model should generate an image of the nine-toothed rake", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 336, "Hint": "Nine-toothed rake."}, {"Prompt": "The animal from the fairy tale Little Red Riding Hood", "Explanation": "The model should generate an image of a wolf", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 337, "Hint": "<PERSON>."}, {"Prompt": "The weapon of <PERSON> from Chinese mythology", "Explanation": "The model should generate an image of the three-pointed spear", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 338, "Hint": "Three-pointed spear."}, {"Prompt": "<PERSON>' iconic moment", "Explanation": "The model should generate an image of <PERSON> swimming", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 339, "Hint": "<PERSON> swimming."}, {"Prompt": "The iconic moment of <PERSON>, in which he took the first steps on a new surface", "Explanation": "The model should generate an image of <PERSON> stepping onto the moon", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 340, "Hint": "<PERSON> stepping onto the moon."}, {"Prompt": "An iconic structure designed by <PERSON><PERSON> in Barcelona", "Explanation": "The model should generate an image of the Sagrada Familia", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 341, "Hint": "Sagrada Familia."}, {"Prompt": "Show an image depicting a popular character from Japanese animation, known for their spiky blond hair and distinctive headband", "Explanation": "The model should generate an image of <PERSON><PERSON><PERSON>, a popular anime character with spiky blond hair and headband", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 342, "Hint": "<PERSON><PERSON><PERSON> with spiky blond hair and a distinctive headband."}, {"Prompt": "The iconic hat of the protagonist of One Piece in Japan", "Explanation": "The model should generate an image of a straw hat with a red ribbon around it, commonly associated with <PERSON>, the main character of the anime and manga series 'One Piece'.", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 343, "Hint": "A straw hat with a red ribbon around it."}, {"Prompt": "The item held by <PERSON><PERSON> in the Romance of the Three Kingdoms", "Explanation": "The model should generate an image of feather fan", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 344, "Hint": "Feather fan."}, {"Prompt": "The weapon famously wielded by Captain <PERSON> in Marvel", "Explanation": "The model should generate an image of a shield", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 345, "Hint": "Captain <PERSON>'s shield."}, {"Prompt": "<PERSON>'s most famous painting", "Explanation": "The model should generate an image of the Mona Lisa", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 346, "Hint": "<PERSON>."}, {"Prompt": "<PERSON>'s most famous art", "Explanation": "The model should generate an image of <PERSON> <PERSON>'s <PERSON>", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 347, "Hint": "<PERSON>."}, {"Prompt": "<PERSON>'s greatest hobby", "Explanation": "The model should generate an image of the scene of painting", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 348, "Hint": "Painting."}, {"Prompt": "The weapon used by Black Panther in Marvel", "Explanation": "The model should generate an image of Black Panther's vibranium claws", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 349, "Hint": "Black Panther's vibranium claws."}, {"Prompt": "The weapon used by <PERSON><PERSON> in the Romance of the Three Kingdoms", "Explanation": "The model should generate an image of the Sky Piercer, the iconic halberd wielded by <PERSON><PERSON>", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 350, "Hint": "Sky Piercer halberd."}, {"Prompt": "<PERSON>'s iconic moment", "Explanation": "The model should generate an image of a Formula 1 car racing scene", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 351, "Hint": "Formula 1 car racing scene."}, {"Prompt": "<PERSON>'s greatest invention", "Explanation": "The model should generate an image of the telephone", "Category": "Cultural knowledge", "Subcategory": "Celebrity", "prompt_id": 352, "Hint": "Telephone."}, {"Prompt": "Typical food for a birthday celebration", "Explanation": "The model should generate an image of a birthday cake", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 353, "Hint": "Birthday cake."}, {"Prompt": "A common pet that meows", "Explanation": "The model should generate an image of a cat", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 354, "Hint": "Cat."}, {"Prompt": "A traditional mode of transportation in Egypt", "Explanation": "The model should generate an image of a camel", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 355, "Hint": "Camel."}, {"Prompt": "Show an image of the tool that is commonly used to drive nails", "Explanation": "The model should generate an image of a hammer", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 356, "Hint": "<PERSON>."}, {"Prompt": "The grain that rice is derived from", "Explanation": "The model should generate an image of rice stalks", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 357, "Hint": "Rice stalks."}, {"Prompt": "Common tool that protects from the rain", "Explanation": "The model should generate an image of an umbrella", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 358, "Hint": "Umbrella."}, {"Prompt": "The military tool used to observe distant enemy movements", "Explanation": "The model should generate an image of a binocular or a telescope", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 359, "Hint": "Binoculars or a telescope."}, {"Prompt": "A Art with black and white keys", "Explanation": "The model should generate an image of a piano", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 360, "Hint": "Piano."}, {"Prompt": "The appliance used in daily life to cook food with strong pressure", "Explanation": "The model should generate an image of a pressure cooker", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 361, "Hint": "Pressure cooker."}, {"Prompt": "The device specifically used for taking photographs", "Explanation": "The model should generate an image of a camera", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 362, "Hint": "Camera."}, {"Prompt": "The most common pointing device for computers", "Explanation": "The model should generate an image of a computer mouse", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 363, "Hint": "Computer mouse."}, {"Prompt": "A small device that can be used to see things close, commonly used by detectives", "Explanation": "The model should generate an image of a magnifying glass", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 364, "Hint": "Magnifying glass."}, {"Prompt": "The key ingredient in making zongzi", "Explanation": "The model should generate an image of glutinous rice", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 365, "Hint": "Glutinous rice."}, {"Prompt": "A popular amusement park ride that spins around and rises up and down", "Explanation": "The model should generate an image of a Ferris wheel", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 366, "Hint": "Ferris wheel."}, {"Prompt": "The transportation inspired by the characteristics of birds", "Explanation": "The model should generate an image of an airplane, designed based on the characteristics of birds, such as flight and aerodynamics", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 367, "Hint": "An airplane designed with characteristics inspired by birds, showcasing flight and aerodynamics."}, {"Prompt": "A place where books are kept and borrowed", "Explanation": "The model should generate an image of a library", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 368, "Hint": "Library"}, {"Prompt": "The object often used to keep light away while sleeping", "Explanation": "The model should generate an image of a sleep mask or blindfold", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 369, "Hint": "Sleep mask."}, {"Prompt": "The fruit used to make candied hawthorn", "Explanation": "The model should generate an image of hawthorn berries", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 370, "Hint": "Hawthorn berries."}, {"Prompt": "The device used to measure temperature", "Explanation": "The model should generate an image of a thermometer", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 371, "Hint": "Thermometer."}, {"Prompt": "The most common input device for computers", "Explanation": "The model should generate an image of a keyboard", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 372, "Hint": "Keyboard."}, {"Prompt": "Most representative currency of the European Union", "Explanation": "The model should generate an image of the Euro, showcasing its design and features", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 373, "Hint": "Euro."}, {"Prompt": "The animal most commonly used to guide the blind", "Explanation": "The model should generate an image of a guide dog", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 374, "Hint": "Guide dog."}, {"Prompt": "The grain used to make popcorn", "Explanation": "The model should generate an image of corn", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 375, "Hint": "Corn."}, {"Prompt": "A common piece of clothing that protects from the rain", "Explanation": "The model should generate an image of the raincoat", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 376, "Hint": "<PERSON>coat."}, {"Prompt": "The ancient Chinese invention used for navigation during maritime exploration", "Explanation": "The model should generate an image of a traditional Chinese compass", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 377, "Hint": "Traditional Chinese compass."}, {"Prompt": "The common tool used for eating steak in Western culture", "Explanation": "The model should generate an image of the knife and fork", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 378, "Hint": "Knife and fork."}, {"Prompt": "Most commonly used currency in the world", "Explanation": "The model should generate an image of the US Dollar, showcasing its design and features", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 379, "Hint": "US Dollar."}, {"Prompt": "The grain used in the production of beer", "Explanation": "The model should generate an image of barley", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 380, "Hint": "<PERSON><PERSON>."}, {"Prompt": "The currency of a populous South Asian nation", "Explanation": "The model should generate an image related to the Indian Rupee, showcasing its design and features", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 381, "Hint": "Indian Rupee."}, {"Prompt": "The item worn to protect the head while riding a motorcycle", "Explanation": "The model should generate an image of a motorcycle helmet", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 382, "Hint": "Motorcycle helmet."}, {"Prompt": "The most important organ responsible for gas exchange in humans", "Explanation": "The model should generate an image of human lungs", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 383, "Hint": "Human lungs."}, {"Prompt": "A vehicle used for public transportation in a city", "Explanation": "The model should generate an image of a bus", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 384, "Hint": "Bus."}, {"Prompt": "The invention, one of the Four Great Inventions of ancient China that could be used to create explosive devices", "Explanation": "The model should generate an image of gunpowder", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 385, "Hint": "Gunpowder."}, {"Prompt": "Show a device commonly used to heat up a liquid like water, for tea or coffee", "Explanation": "The model should generate an image of a kettle", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 386, "Hint": "<PERSON><PERSON>."}, {"Prompt": "A tool for cutting paper", "Explanation": "The model should generate an image of a pair of scissors", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 387, "Hint": "A pair of scissors."}, {"Prompt": "A mythical creature that breathes fire and is often depicted in medieval stories", "Explanation": "The model should generate an image of a dragon", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 388, "Hint": "Dragon."}, {"Prompt": "Most representative currency of the United Kingdom", "Explanation": "The model should generate an image of the British Pound, showcasing its design and features", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 389, "Hint": "British Pound."}, {"Prompt": "The astronomical tool used for observing celestial bodies", "Explanation": "The model should generate an image of a telescope", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 390, "Hint": "Telescope."}, {"Prompt": "The item worn by Chinese primary school students during the flag-raising ceremony", "Explanation": "The model should generate an image of a red scarf", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 391, "Hint": "Red scarf."}, {"Prompt": "The main ingredients used to make fried dough sticks", "Explanation": "The model should generate an image of flour", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 392, "Hint": "Flour."}, {"Prompt": "A large, traditional vehicle used for travel in cold climates, often pulled by dogs", "Explanation": "The model should generate an image of a dog sled", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 393, "Hint": "Dog sled"}, {"Prompt": "A structure made of ice, traditionally built by the Inuit people", "Explanation": "The model should generate an image of an igloo", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 394, "Hint": "Igloo."}, {"Prompt": "A small device used to control electronic devices remotely", "Explanation": "The model should generate an image of a remote control", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 395, "Hint": "Remote control."}, {"Prompt": "The official currency of the second largest economy in the world", "Explanation": "The model should generate an image of the Chinese Yuan, showcasing its design and features", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 396, "Hint": "Chinese Yuan."}, {"Prompt": "The appliance used in daily life to keep food fresh", "Explanation": "The model should generate an image of a refrigerator", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 397, "Hint": "Refrigerator."}, {"Prompt": "The traditional Japanese garment often worn during festivals", "Explanation": "The model should generate an image of a kimono", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 398, "Hint": "<PERSON><PERSON>."}, {"Prompt": "A common object used to keep hair out of face, often made of plastic or metal", "Explanation": "The model should generate an image of a hair clip or barrette", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 399, "Hint": "Hair clip or barrette."}, {"Prompt": "Show an image of an object people often use to look at a reflection of themselves", "Explanation": "The model should generate an image of a mirror", "Category": "Cultural knowledge", "Subcategory": "Life", "prompt_id": 400, "Hint": "Mirror."}, {"Prompt": "An apple tree with leaves exhibiting chlorophyll breakdown", "Explanation": "The model should generate an image of an apple tree where the leaves are predominantly yellow, orange, red, or brown (a mix of these colors is acceptable). The generated image should evoke the feeling of autumn", "Category": "Biology", "Subcategory": "State", "prompt_id": 701, "Hint": "An apple tree with leaves in shades of yellow, orange, red, and brown, evoking the feeling of autumn."}, {"Prompt": "A maple tree with leaves exhibiting chlorophyll breakdown", "Explanation": "The model should generate an image of a maple tree with leaves displaying a vibrant mix of yellow, orange, and red hues. The characteristic palmate shape of maple leaves should be clearly visible. The image should evoke a sense of peak fall foliage", "Category": "Biology", "Subcategory": "State", "prompt_id": 702, "Hint": "A maple tree with leaves displaying a vibrant mix of yellow, orange, and red hues, showcasing the characteristic palmate shape of maple leaves during peak fall foliage."}, {"Prompt": "A birch tree with leaves exhibiting chlorophyll breakdown", "Explanation": "The model should generate an image of a birch tree where the leaves are predominantly yellow (a mix of yellow and some remaining green is acceptable).  The delicate structure of birch leaves should be visible", "Category": "Biology", "Subcategory": "State", "prompt_id": 703, "Hint": "A birch tree with predominantly yellow leaves, showing some remaining green, and the delicate structure of the leaves clearly visible."}, {"Prompt": "A maple tree with leaves where chlorophyll is not broken down", "Explanation": "The model should generate an image of a maple tree  with lush foliage and leaves that are a vibrant, deep green", "Category": "Biology", "Subcategory": "State", "prompt_id": 704, "Hint": "A maple tree with lush foliage and vibrant, deep green leaves."}, {"Prompt": "A caterpillar having completed its pupation process", "Explanation": "The image should show a butterfly or moth with fully formed wings and body", "Category": "Biology", "Subcategory": "State", "prompt_id": 705, "Hint": "A butterfly or moth with fully formed wings and body."}, {"Prompt": "A caterpillar during its pupation process", "Explanation": "The image should show a caterpillar inside a chrysalis or pupa, with physical changes occurring, indicating it is undergoing metamorphosis", "Category": "Biology", "Subcategory": "State", "prompt_id": 706, "Hint": "A caterpillar inside a chrysalis or pupa, undergoing physical changes during metamorphosis."}, {"Prompt": "Image of a tadpole after growing up", "Explanation": "The image should show a frog", "Category": "Biology", "Subcategory": "State", "prompt_id": 707, "Hint": "Frog."}, {"Prompt": "Just laid eggs", "Explanation": "The image should show newly laid eggs that are still intact, often in a nest or in a protected environment", "Category": "Biology", "Subcategory": "State", "prompt_id": 708, "Hint": "Newly laid eggs in a nest or a protected environment."}, {"Prompt": "Eggs which are recently hatched", "Explanation": "The image should show eggs that have hatched, with broken shells, and new life having emerged", "Category": "Biology", "Subcategory": "State", "prompt_id": 709, "Hint": "Recently hatched eggs with broken shells and new life having emerged."}, {"Prompt": "A frog resting in a very humid environment", "Explanation": "The image should depict a frog with smooth, shiny skin, surrounded by wet conditions like wet leaves or mud", "Category": "Biology", "Subcategory": "State", "prompt_id": 710, "Hint": "A frog with smooth, shiny skin, surrounded by wet leaves or mud in a humid environment."}, {"Prompt": "A frog observed during very hot and dry weather", "Explanation": "The image should show a frog with dull, dry skin, in an environment with hot, dry soil and grass, highlighting a lack of moisture", "Category": "Biology", "Subcategory": "State", "prompt_id": 711, "Hint": "A frog with dull, dry skin in an environment with hot, dry soil and grass, highlighting a lack of moisture."}, {"Prompt": "A cactus seen in an environment with considerable moisture", "Explanation": "The image should show a cactus with softer, less prominent spines, possibly with water accumulation around the base, and more surrounding vegetation", "Category": "Biology", "Subcategory": "State", "prompt_id": 712, "Hint": "A cactus with softer, less prominent spines, surrounded by vegetation and water accumulation around its base."}, {"Prompt": "A cactus seen in a very arid, desert-like environment", "Explanation": "The image should show a cactus with prominent, dense spines, possibly with a thick, waxy coating on its skin, and surrounded by sparse, dry vegetation, sand, or rocks The overall color palette should be warm and earthy, indicating dryness and heat", "Category": "Biology", "Subcategory": "State", "prompt_id": 713, "Hint": "A cactus with prominent, dense spines and a thick, waxy coating on its skin, surrounded by sparse, dry vegetation, sand, or rocks in a warm, earthy, arid desert environment."}, {"Prompt": "A fresh loaf of bread", "Explanation": "The model should generate an image showing a loaf of bread with a golden-brown crust and a soft, uniform interior, representing its freshly baked and edible state", "Category": "Biology", "Subcategory": "State", "prompt_id": 714, "Hint": "A loaf of bread with a golden-brown crust and a soft, uniform interior, representing its freshly baked and edible state."}, {"Prompt": "Moldy bread", "Explanation": "The model should generate an image showing a loaf of bread with visible patches of mold, appearing as fuzzy or discolored growths on the surface The mold should be clearly recognizable and unappetizing", "Category": "Biology", "Subcategory": "State", "prompt_id": 715, "Hint": "A loaf of bread with visible patches of mold, appearing as fuzzy or discolored growths on the surface."}, {"Prompt": "A rotting log in a forest", "Explanation": "The model should generate an image of a decaying log covered in fungi, moss, and other organisms Showcasing that it can turn into soil and other source of carbon Showing a good cycle", "Category": "Biology", "Subcategory": "State", "prompt_id": 716, "Hint": "A decaying log in a forest covered in fungi, moss, and other organisms, showcasing decomposition and the natural cycle of turning into soil."}, {"Prompt": "A seed before germination", "Explanation": "The model should generate an image showing a seed before germination, with the grain and everything", "Category": "Biology", "Subcategory": "State", "prompt_id": 717, "Hint": "A seed with its grain intact before germination."}, {"Prompt": "A seed during germination", "Explanation": "The model should generate an image showing a seed during the germination You can see the root growing", "Category": "Biology", "Subcategory": "State", "prompt_id": 718, "Hint": "A seed with a root growing during germination."}, {"Prompt": "A fresh wound on human", "Explanation": "The model should generate an image showing the area is injured, and is still bleeding The wound edges should appear raw and inflamed", "Category": "Biology", "Subcategory": "State", "prompt_id": 719, "Hint": "A fresh, bleeding wound with raw, inflamed edges on human skin."}, {"Prompt": "A wound on human after recovery", "Explanation": "The model should generate an image showing the wound is fully healed The wound site should show evidence of the healing process, such as a scab or a visible scar The surrounding skin may still have some discoloration", "Category": "Biology", "Subcategory": "State", "prompt_id": 720, "Hint": "A fully healed wound on human skin, showing signs of the healing process such as a scab or a visible scar, with surrounding skin exhibiting slight discoloration."}, {"Prompt": "A piece of meat before the bacteria growing", "Explanation": "The model should generate an image with red meat in a good condition, representing a health product", "Category": "Biology", "Subcategory": "State", "prompt_id": 721, "Hint": "Red meat in good condition, fresh and healthy."}, {"Prompt": "A piece of meat during bacterial decomposition", "Explanation": "The model should generate an image with meat that has some white thing grow, is not in good condition, which represent something not safe to eat", "Category": "Biology", "Subcategory": "State", "prompt_id": 722, "Hint": "A piece of meat with visible white growths and signs of spoilage, representing an unsafe condition for consumption."}, {"Prompt": "A normal red blood cell in isotonic solution", "Explanation": "The model should generate an image of red blood cells with a biconcave disc shape, maintaining their normal size and shape", "Category": "Biology", "Subcategory": "State", "prompt_id": 723, "Hint": "Red blood cells with a biconcave disc shape, maintaining their normal size and shape in an isotonic solution."}, {"Prompt": "Red blood cells in hypertonic solution", "Explanation": "The model should generate an image of red blood cells that look shrunken or crenated (spiky), due to water loss", "Category": "Biology", "Subcategory": "State", "prompt_id": 724, "Hint": "Shrunken, spiky red blood cells caused by water loss in a hypertonic solution."}, {"Prompt": "Red blood cells in hypotonic solution", "Explanation": "The model should generate an image of red blood cells that appear swollen and spherical, some may be bursting, due to water gain", "Category": "Biology", "Subcategory": "State", "prompt_id": 725, "Hint": "Swollen and spherical red blood cells, some bursting, due to water gain in a hypotonic solution."}, {"Prompt": "A lake with a large algae bloom", "Explanation": "The image generated must show a lake The water should appear green, brown, red, or another unusual color, indicating a high concentration of algae The bloom may cover a significant portion of the water's surface", "Category": "Biology", "Subcategory": "State", "prompt_id": 726, "Hint": "A lake with water appearing green, brown, red, or another unusual color, indicating a high concentration of algae, with the bloom covering a significant portion of the water's surface."}, {"Prompt": "A leaf infected by fungus", "Explanation": "The image should show a leaf with distinct black or yellow mold spots, indicating fungal infection on the surface", "Category": "Biology", "Subcategory": "State", "prompt_id": 727, "Hint": "A leaf with distinct black or yellow mold spots, indicating fungal infection on the surface."}, {"Prompt": "A potted plant receiving consistent sunlight for a week", "Explanation": "The image should show a healthy potted plant with vibrant green leaves and upright stems, indicating it has received sufficient light for growth", "Category": "Biology", "Subcategory": "State", "prompt_id": 728, "Hint": "A healthy potted plant with vibrant green leaves and upright stems, indicating it has received sufficient light for growth."}, {"Prompt": "A potted succulent placed in a completely dark closet for one week", "Explanation": "The image should show a wilted potted plant with drooping leaves, pale stems, and potentially elongated internodes, indicating a lack of light", "Category": "Biology", "Subcategory": "State", "prompt_id": 729, "Hint": "A wilted potted succulent with drooping leaves, pale stems, and elongated internodes, indicating a lack of light."}, {"Prompt": "A recently peeled apple slice segment", "Explanation": "The image should depict an apple slice with fresh, moist flesh and minimal browning, indicating it was recently cut", "Category": "Biology", "Subcategory": "State", "prompt_id": 730, "Hint": "A fresh apple slice with moist flesh and minimal browning, indicating it was recently cut."}, {"Prompt": "An apple slice, peeled and left out for a long time", "Explanation": "The image should show an apple slice with browned flesh and a slightly dry surface, indicating it has been exposed to air for a while", "Category": "Biology", "Subcategory": "State", "prompt_id": 731, "Hint": "An apple slice with browned flesh and a slightly dry surface, indicating it has been exposed to air for a while."}, {"Prompt": "Dehydrated roses", "Explanation": "The model should generate an image of roses showing signs of severe dehydration. This should include drooping petals and stems, wilting leaves. The roses should appear listless and lacking their usual vibrancy", "Category": "Biology", "Subcategory": "State", "prompt_id": 732, "Hint": "Roses with drooping petals and stems, wilting leaves, appearing listless and lacking vibrancy."}, {"Prompt": "An overripe banana", "Explanation": "The image should show a banana with dark spots, a soft texture, and possibly some bruising, indicating it is overripe", "Category": "Biology", "Subcategory": "State", "prompt_id": 733, "Hint": "An image of a banana with dark spots, a soft texture, and some bruising, indicating it is overripe."}, {"Prompt": "A premature banana", "Explanation": "The image should show a banana with a firm texture, and a light green peel, indicating it is not fully ripe", "Category": "Biology", "Subcategory": "State", "prompt_id": 734, "Hint": "A banana with a firm texture and a light green peel, indicating it is not fully ripe."}, {"Prompt": "The state of a leaf after a cold night", "Explanation": "The model should generate an image of a leaf covered in frost crystals in early morning", "Category": "Biology", "Subcategory": "State", "prompt_id": 735, "Hint": "A leaf covered in frost crystals in early morning."}, {"Prompt": "A pine tree in severe drought", "Explanation": "The image should show a pine tree with dry, brown needles and parched soil, indicating a state of drought", "Category": "Biology", "Subcategory": "State", "prompt_id": 736, "Hint": "A pine tree with dry, brown needles and parched soil, indicating a state of drought."}, {"Prompt": "Rice field with phosphorus deficiency", "Explanation": "The model should generate an image of a rice field exhibiting symptoms of phosphorus deficiency. This should include stunted growth, dark green leaves (sometimes with a reddish or purplish tinge), and reduced tillering (number of stems per plant). The overall field should appear unproductive and unhealthy", "Category": "Biology", "Subcategory": "State", "prompt_id": 737, "Hint": "A rice field with stunted growth, dark green leaves with reddish or purplish tinge, reduced tillering, and an unproductive, unhealthy appearance."}, {"Prompt": "Apple tree with iron deficiency", "Explanation": "The model should generate an image of an apple tree exhibiting symptoms of iron deficiency (chlorosis). This should include yellowing of young leaves, with the veins remaining green (interveinal chlorosis). The overall tree may appear pale and stunted", "Category": "Biology", "Subcategory": "State", "prompt_id": 738, "Hint": "An apple tree with yellowing young leaves showing green veins (interveinal chlorosis), appearing pale and stunted, exhibiting symptoms of iron deficiency."}, {"Prompt": "Nitrogen-deficient wheat field", "Explanation": "The model should generate an image of a wheat field where a significant portion of the plants show abnormally short stalks (stunted growth) compared to healthy wheat. The leaves may also be yellowed (chlorotic), especially the older ones", "Category": "Biology", "Subcategory": "State", "prompt_id": 739, "Hint": "A wheat field with a significant portion of the plants showing abnormally short stalks and yellowed leaves, especially the older ones, indicating nitrogen deficiency."}, {"Prompt": "Potassium-deficient banana tree", "Explanation": "The model should generate an image of a banana tree specifically showing 'leaf scorch,' where the edges of the leaves are brown, dry, and appear burned. The older leaves will be affected more severely. The image should still display its signature green to demonstrate it is alive", "Category": "Biology", "Subcategory": "State", "prompt_id": 740, "Hint": "A banana tree with leaves showing brown, dry, scorched edges, with older leaves more severely affected, while the rest of the tree remains green and alive."}, {"Prompt": "Calcium-deficient pepper plants", "Explanation": "The model should generate an image of pepper plants characterized by blossom-end rot on the developing peppers. This will show as dark, sunken lesions at the blossom end of the fruit. The leaves may also be distorted or curled", "Category": "Biology", "Subcategory": "State", "prompt_id": 741, "Hint": "Pepper plants with dark, sunken lesions at the blossom end of the fruit, showing signs of blossom-end rot, along with distorted or curled leaves."}, {"Prompt": "Boron-deficient broccoli with hollow stem", "Explanation": "The model should generate an image of a broccoli head exhibiting symptoms of boron deficiency, including a hollow stem and/or a brown discoloration in the center of the head. The leaves may also be thick, brittle, and curled", "Category": "Biology", "Subcategory": "State", "prompt_id": 742, "Hint": "Broccoli head with a hollow stem, brown discoloration in the center, and thick, brittle, curled leaves showing signs of boron deficiency."}, {"Prompt": "Skin scalded by hot water", "Explanation": "The model should generate an image of skin and the skin should be reddened", "Category": "Biology", "Subcategory": "State", "prompt_id": 743, "Hint": "Reddened skin caused by hot water."}, {"Prompt": "Beehive, emphasizing its structural construction", "Explanation": "The model should generate an image of a beehive, with a strong emphasis on the intricate hexagonal structure of the honeycomb. The overall image should convey a sense of organized complexity", "Category": "Biology", "Subcategory": "State", "prompt_id": 744, "Hint": "A beehive showcasing the intricate hexagonal structure of the honeycomb, conveying a sense of organized complexity."}, {"Prompt": "A fox in winter, highlighting the state of its fur", "Explanation": "The image should show a fox with a thick, dense fur, appearing fluffy and well-insulated against the cold. There may be snow or other winter elements in the scene", "Category": "Biology", "Subcategory": "State", "prompt_id": 745, "Hint": "A fox with thick, dense, fluffy fur in a winter setting, possibly surrounded by snow."}, {"Prompt": "Arctic fox in snow", "Explanation": "The model should generate an image of an Arctic fox in its winter coat, blending seamlessly with a snowy landscape", "Category": "Biology", "Subcategory": "State", "prompt_id": 746, "Hint": "Arctic fox in its winter coat, blending seamlessly with a snowy landscape."}, {"Prompt": "Stonefish on ocean floor", "Explanation": "The model should generate an image of a stonefish resting on the ocean floor, with its body camouflaged to resemble the surrounding rocks and sediment", "Category": "Biology", "Subcategory": "State", "prompt_id": 747, "Hint": "A stonefish resting on the ocean floor, its body camouflaged to resemble the surrounding rocks and sediment."}, {"Prompt": "Eutrophic lake", "Explanation": "The model should generate an image of a lake exhibiting signs of eutrophication. The water should appear green due to a high concentration of algae (phytoplankton)", "Category": "Biology", "Subcategory": "State", "prompt_id": 748, "Hint": "A lake with green water due to a high concentration of algae (phytoplankton), exhibiting signs of eutrophication."}, {"Prompt": "Stationary stick insect", "Explanation": "The model should generate an image of a stick insect that is extremely difficult to distinguish from its surroundings. The stick insect should be positioned on a branch or among leaves, and its color, shape, and texture should blend seamlessly with the environment", "Category": "Biology", "Subcategory": "State", "prompt_id": 749, "Hint": "A stick insect blending seamlessly with its surroundings, positioned on a branch or among leaves, with its color, shape, and texture mimicking the environment."}, {"Prompt": "Skin after a mosquito bite", "Explanation": "The model should generate an image of human skin showing a raised, red bump caused by a mosquito bite. The surrounding skin may also be slightly reddened or inflamed", "Category": "Biology", "Subcategory": "State", "prompt_id": 750, "Hint": "Human skin showing a raised, red bump caused by a mosquito bite, with surrounding skin slightly reddened or inflamed."}, {"Prompt": "Siblings of identical twins", "Explanation": "The model should generate two babies who look extremely similar, almost identical", "Category": "Biology", "Subcategory": "State", "prompt_id": 751, "Hint": "Two identical twin babies."}, {"Prompt": "Siblings of fraternal twins", "Explanation": "The model should generate images of two infants, who have significant differences", "Category": "Biology", "Subcategory": "State", "prompt_id": 752, "Hint": "Two infants with significant differences in appearance."}, {"Prompt": "Mimosa pudica leaf being touched", "Explanation": "The model should generate an image of a Mimosa pudica with its leaves in the process of folding or closing in response to touch. Some leaflets should be fully closed, while others may be in the process of closing", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 753, "Hint": "A Mimosa pudica with its leaves in the process of folding or closing in response to touch, with some leaflets fully closed and others in the process of closing."}, {"Prompt": "A chameleon perfectly camouflaged against a green leaf", "Explanation": "The image should show a chameleon with its skin color and pattern closely matching the green color and texture of a surrounding leaf", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 754, "Hint": "A chameleon with its skin color and pattern closely matching the green color and texture of a surrounding leaf."}, {"Prompt": "Frogs mating and laying eggs", "Explanation": "The model should generate an image of two frogs in amplexus (mating embrace), with the male clinging to the female's back", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 755, "Hint": "Two frogs in amplexus (mating embrace), with the male clinging to the female's back."}, {"Prompt": "Migrating geese, emphasizing the shape of the formation", "Explanation": "The model should generate an image of a flock of geese flying in a characteristic V-formation", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 756, "Hint": "A flock of geese flying in a characteristic V-formation."}, {"Prompt": "Dog marking its territory", "Explanation": "The model should generate an image of a dog urinating. The urine should be visible", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 757, "Hint": "A dog urinating with visible urine."}, {"Prompt": "A chameleon perfectly camouflaged against a brown leaf", "Explanation": "The image should show a chameleon with its skin color and pattern closely matching the brown color and texture of a dry leaf or soil", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 758, "Hint": "A chameleon with its skin color and pattern closely matching the brown color and texture of a dry leaf or soil."}, {"Prompt": "A Mimosa pudica plant before being touched", "Explanation": "The model should generate an image showing a Mimosa pudica (sensitive plant) with its leaves fully open and extended, representing its normal, undisturbed state The leaves should appear healthy and vibrant", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 759, "Hint": "A Mimosa pudica plant with its leaves fully open and extended, appearing healthy and vibrant."}, {"Prompt": "Lion marking its territory", "Explanation": "The model should generate an image of a lion standing near a tree and exhibiting territorial marking behavior. This should include visible claw marks on the tree trunk", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 760, "Hint": "A lion standing near a tree with visible claw marks on the tree trunk, exhibiting territorial marking behavior."}, {"Prompt": "A Mimosa pudica plant after being touched", "Explanation": "The model should generate an image showing a Mimosa pudica plant with its leaves folded inward and drooping, demonstrating its rapid response to touch The image should clearly illustrate the plant's defensive mechanism", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 761, "Hint": "A Mimosa pudica plant with its leaves folded inward and drooping, demonstrating its rapid response to touch."}, {"Prompt": "Vultures feeding", "Explanation": "The model should generate an image of vultures actively feeding on carrion. The scene should be realistic and depict the vultures in a setting appropriate for scavenging, such as a dry grassland, desert, or open savanna. The depiction of carrion should be present and reasonably detailed", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 762, "Hint": "Vultures feeding on carrion in a realistic setting such as a dry grassland, desert, or open savanna, with detailed carrion depiction."}, {"Prompt": "Porcupine defending itself from a predator", "Explanation": "The model should generate an image of a porcupine exhibiting defensive behaviors in response to a perceived threat. The porcupine should be displaying its spines prominently, raised or fanned out", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 763, "Hint": "A porcupine with its spines prominently raised or fanned out in a defensive posture."}, {"Prompt": "Pangolin defending itself from a predator", "Explanation": "The model should generate an image of a pangolin exhibiting its primary defensive behavior: curling into a tight ball. The scales should be prominently displayed, forming a protective armor around the animal. The head and limbs should be tucked inside the ball", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 764, "Hint": "A pangolin curled into a tight ball, prominently displaying its protective scales as armor, with its head and limbs tucked inside."}, {"Prompt": "Armadillo threatened by a predator", "Explanation": "The model should generate an image of an armadillo reacting to a perceived threat. Ideally, the armadillo should be depicted curling into a ball, with its armored plates providing protection", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 765, "Hint": "An armadillo curling into a ball, with its armored plates providing protection, reacting to a perceived threat."}, {"Prompt": "Hedgehog defending itself from a predator", "Explanation": "The model should generate an image of a hedgehog curled into a tight ball, with its spines fully erect. The predator (e.g., fox, badger) may be nearby but not directly attacking. The environment should be a woodland or garden setting. The image should convey a sense of prickly defensiveness", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 766, "Hint": "A hedgehog curled into a tight ball with its spines fully erect in a woodland or garden setting, with a nearby predator such as a fox or badger, conveying a sense of prickly defensiveness."}, {"Prompt": "Opossum's common behavior to avoid a predator", "Explanation": "The model should generate an image of an opossum lying on its side with its mouth open and tongue lolling out, mimicking a dead animal", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 767, "Hint": "An opossum lying on its side with its mouth open and tongue lolling out, mimicking a dead animal."}, {"Prompt": "Frilled-neck lizard defending itself from a predator", "Explanation": "The model should generate an image of a frilled-neck lizard displaying its frill, with its mouth open and standing on its hind legs. The environment should be a tropical woodland or savanna", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 768, "Hint": "Frilled-neck lizard displaying its frill, with its mouth open and standing on its hind legs in a tropical woodland or savanna environment."}, {"Prompt": "Box turtle defending itself from a predator", "Explanation": "The model should generate an image of a box turtle fully retracted into its shell, with the hinged shell tightly closed. A predator (e.g., raccoon, dog) might be investigating the shell", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 769, "Hint": "A box turtle fully retracted into its tightly closed, hinged shell, with a predator like a raccoon or dog investigating it."}, {"Prompt": "A frigatebird displaying to attract a mate", "Explanation": "The model should generate an image of a male frigatebird with its bright red gular sac fully inflated. The inflated sac should be the most prominent visual element, clearly distinguishing the male. A female frigatebird may be visible nearby", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 770, "Hint": "A male frigatebird with its bright red gular sac fully inflated, prominently displayed, with a female frigatebird visible nearby."}, {"Prompt": "Fireflies displaying to attract mates", "Explanation": "The model should generate an image of numerous fireflies flashing their bioluminescent lights in a dark environment, such as a field, forest, or swamp at night", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 771, "Hint": "Numerous fireflies flashing their bioluminescent lights in a dark environment, such as a field, forest, or swamp at night."}, {"Prompt": "Describe the behavior of a sloth most of the time", "Explanation": "The model should generate an image of a sloth hanging from a tree branch in a rainforest environment. The sloth should appear relaxed and still or moving very slowly", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 772, "Hint": "A sloth hanging from a tree branch in a rainforest environment, appearing relaxed and still or moving very slowly."}, {"Prompt": "Electric eel displaying a common behavior for predation or defense", "Explanation": "The model should generate an image of an electric eel in a murky, freshwater environment, such as a river or swamp. The eel should be depicted generating an electric discharge", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 773, "Hint": "An electric eel in a murky, freshwater environment like a river or swamp, generating an electric discharge."}, {"Prompt": "Describe a water lily flower closing", "Explanation": "The model should generate an image of a water lily with its flower closed or partially closed. The image should accurately convey a nighttime setting", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 774, "Hint": "A water lily flower partially closing in a nighttime setting."}, {"Prompt": "Describe a water lily flower fully open and facing upwards", "Explanation": "The model should generate an image of a water lily with its flower fully open and facing upwards. The image should accurately convey a daytime setting, with bright, natural lighting", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 775, "Hint": "A fully open water lily flower facing upwards in bright, natural daytime lighting."}, {"Prompt": "Morning rooster, emphasizing its behavior", "Explanation": "The image should show a rooster with its beak open and neck stretched upwards, indicating crowing", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 776, "Hint": "A rooster with its beak open and neck stretched upwards, indicating crowing in the morning."}, {"Prompt": "Bats resting in the trees", "Explanation": "The image should show one or more bats hanging upside down from a tree branch", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 777, "Hint": "Bats hanging upside down from a tree branch."}, {"Prompt": "A cheetah preparing to sprint towards its prey", "Explanation": "The image should show a cheetah crouched, focusing intently on its prey with a tense body posture in a savannah setting", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 778, "Hint": "A crouched cheetah in a savannah setting, focusing intently on its prey with a tense body posture."}, {"Prompt": "Octopus behavior when facing danger", "Explanation": "The image should show an octopus ejecting a cloud of dark ink", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 779, "Hint": "An octopus ejecting a cloud of dark ink."}, {"Prompt": "Jellyfish in the darkness", "Explanation": "The image should show a jellyfish emitting light", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 780, "Hint": "A glowing jellyfish in the darkness."}, {"Prompt": "Lizard's extreme escape behavior when facing danger", "Explanation": "The image should show a lizard detaching its tail", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 781, "Hint": "A lizard detaching its tail."}, {"Prompt": "Common behavior after a whale surfaces", "Explanation": "The image should show whale spraying", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 782, "Hint": "Whale spraying water from its blowhole after surfacing."}, {"Prompt": "Sunflowers and the sun", "Explanation": "The model should generate an image of sunflowers with their heads oriented towards the sun. The sun should be visible in the sky, bright and clear", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 783, "Hint": "Sunflowers with their heads oriented towards a bright, clear sun in the sky."}, {"Prompt": "A male peacock trying to attract a female", "Explanation": "The image should show a peacock with its tail feathers fully extended and displayed, showcasing vibrant colors and patterns used for courtship", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 784, "Hint": "A male peacock with its tail feathers fully extended, showcasing vibrant colors and intricate patterns for courtship."}, {"Prompt": "A bird grooming its feathers", "Explanation": "The image should show a bird using its beak to carefully groom its feathers", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 785, "Hint": "A bird using its beak to groom its feathers."}, {"Prompt": "A bear in the cold winter months", "Explanation": "The image should show a bear curled up and sleeping, with its body nestled in a den or under thick layers of snow, indicating it is in hibernation during the cold winter period", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 786, "Hint": "A bear curled up and sleeping, nestled in a den or under thick layers of snow during hibernation in the cold winter months."}, {"Prompt": "Wildebeest migration on the African savanna", "Explanation": "The model should generate an image of thousands of wildebeest running across the African savanna, kicking up dust", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 787, "Hint": "Thousands of wildebeest running across the African savanna, kicking up dust."}, {"Prompt": "Cat grooming itself", "Explanation": "The model should generate an image of a cat using its tongue to groom its fur. The cat should be licking its fur, and the tongue should be visibly extended. The cat should appear relaxed and content", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 788, "Hint": "A cat using its tongue to groom its fur, with its tongue visibly extended, appearing relaxed and content."}, {"Prompt": "Venus flytrap catching an insect", "Explanation": "The model should generate an image of a Venus flytrap with its leaves snapped shut around an insect (e.g., fly, ant). The trap should be tightly closed", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 789, "Hint": "Venus flytrap with its leaves snapped shut around an insect like a fly or ant, the trap tightly closed."}, {"Prompt": "Gorilla displaying dominance", "Explanation": "The model should generate an image of a male gorilla standing upright and beating its chest with its hands. The gorilla should appear large and imposing", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 790, "Hint": "Male gorilla standing upright, beating its chest with its hands, appearing large and imposing."}, {"Prompt": "Kangaroo nursing its joey", "Explanation": "The model should generate an image of a female kangaroo nursing a joey (baby kangaroo). The joey should be attached to the nipple inside the mother's pouch, with its head and/or upper body visible", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 791, "Hint": "A female kangaroo nursing a joey, with the joey's head and/or upper body visible while attached to the nipple inside the mother's pouch."}, {"Prompt": "Monarch butterfly migration", "Explanation": "The model should generate an image of monarch butterflies, thousands to millions of butterflies should be visible, and the scene should convey a sense of mass migration", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 792, "Hint": "Thousands of monarch butterflies in flight, conveying a sense of mass migration."}, {"Prompt": "Crow extracting insects from a tree cavity", "Explanation": "The model should generate an image of a crow using a tool to extract food. The crow should be positioned near a tree, with its long beak, and should be using a twig, to extract this food", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 793, "Hint": "A crow near a tree using a twig with its beak to extract insects from a tree cavity."}, {"Prompt": "Social heating behavior in penguins", "Explanation": "The model should generate an image of penguins tightly packed and huddling for warmth and they need to be tightly packed and in a out door setting to get an accurate reflection", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 794, "Hint": "Penguins tightly packed and huddling for warmth in an outdoor setting."}, {"Prompt": "<PERSON>rdi<PERSON>'s group defense against predators", "Explanation": "The model should generate an image of a large school of sardines forming a tight, spherical cluster (a bait ball) in the water", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 795, "Hint": "A large school of sardines forming a tight, spherical cluster (a bait ball) in the water."}, {"Prompt": "Elephant using a tool to drive flies", "Explanation": "The model should generate an image of an elephant holding a branch in its trunk to remove flies, and must be accurate in its trunk movements and their behaviors", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 796, "Hint": "An elephant holding a branch in its trunk to remove flies."}, {"Prompt": "Hippo marking its territory", "Explanation": "The model should generate an image of a hippo with its tail raised and flinging dung (feces) into the surrounding environment. The tail movement should be visible, and the flung dung should be clearly depicted", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 797, "Hint": "A hippo with its tail raised, flinging dung into the surrounding environment, with visible tail movement and clearly depicted flung dung."}, {"Prompt": "Zebras forming a defensive circle", "Explanation": "The model should generate an image of a group of zebras forming a tight circle as a defensive technique, with the heads of the zebras facing outward. There may be cubs Inside the circle", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 798, "Hint": "A group of zebras forming a tight circle with their heads facing outward, possibly with cubs inside the circle."}, {"Prompt": "Penguin parents transferring an egg", "Explanation": "The model should generate an image of two penguins using their beaks to carefully transfer an egg. The penguins should be facing each other, and the egg should be delicately balanced between their beaks", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 799, "Hint": "Two penguins facing each other, using their beaks to carefully transfer an egg delicately balanced between them."}, {"Prompt": "Virginia creeper and wall, highlighting the behavior of Virginia creeper", "Explanation": "The model should generate an image illustrating the Virginia creeper's upward growth along the wall, closely adhering to the surface", "Category": "Biology", "Subcategory": "Behavior", "prompt_id": 800, "Hint": "Virginia creeper growing upward along a wall, closely adhering to the surface."}, {"Prompt": "A string of decorative lights hanging from a balcony", "Explanation": "The model should generate an image where the lights hang downwards due to gravity, and not floating in the air or defying gravity The curve of the string should be consistent with a hanging object", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 801, "Hint": "A string of decorative lights hanging down naturally from a balcony, following the curve of gravity."}, {"Prompt": "An elephant and a rabbit stand on both sides of a seesaw", "Explanation": "The model should generate an image where the seesaw is significantly tilted due to the weight difference between the elephant and the rabbit. The elephant should be on the lower side, close to the ground, and the rabbit should be on the higher side, elevated in the air The seesaw should not be level", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 802, "Hint": "An image of a seesaw significantly tilted with an elephant on the lower side close to the ground and a rabbit elevated on the higher side in the air."}, {"Prompt": "An iron ball and an equally sized soccer ball are standing on either side of the balance beam", "Explanation": "The model should generate an image where the beam balance is heavily tilted due to the substantial weight difference between the iron ball and the soccer ball The pan holding the iron ball should be positioned low, possibly touching the ground, and the pan holding the soccer ball should be noticeably higher The balance should be in a static tilted position", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 803, "Hint": "A beam balance heavily tilted, with a heavy iron ball on one side positioned low to the ground and an equally sized soccer ball on the other side positioned higher."}, {"Prompt": "The large stone and the rubber ball are standing on both sides of the beam balance", "Explanation": "The model should generate an image where the beam balance is tilted, indicating that the stone is heavier than the rubber ball The side with the stone should be lower, with a visible angle between the two pans, implying a weight difference, but the stone's side isn't at the extreme point The balance should not be level", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 804, "Hint": "A tilted beam balance with a large stone on the lower side and a rubber ball on the higher side, clearly indicating the stone is heavier, with a visible angle between the two pans but not at an extreme tilt."}, {"Prompt": "The child and the leaf are standing on both sides of the teeter-totter", "Explanation": "The model should generate an image where the teeter-totter is heavily tilted due to the very different weights of the child and the leaf The side with the child should be almost touching the ground, and the other end with the leaf should be high in the air The teeter-totter should not be level", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 805, "Hint": "A teeter-totter heavily tilted with a child on one end near the ground and a leaf on the other end high in the air."}, {"Prompt": "Two gold balls, one large and one small, fall from the air Compare their height at the same moment", "Explanation": "The model should generate an image where the two gold balls are at approximately the same height at any given moment during their fall, reflecting that their falling speed is independent of size due to gravity", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 806, "Hint": "Two gold balls, one large and one small, falling through the air at approximately the same height, reflecting their equal falling speed due to gravity."}, {"Prompt": "A hanging plumb bob and the ground", "Explanation": "The model should generate an image where the plumb bob hangs vertically downwards due to gravity, with the plumb line perpendicular to the ground The line and plumb bob should be in a straight line pointing directly to the ground", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 807, "Hint": "A plumb bob hanging vertically downward with a straight plumb line perpendicular to the ground."}, {"Prompt": "A hanging melon next to the ground", "Explanation": "The model should generate an image where the hanging melon hangs vertically downwards due to gravity, with the hanging structure (e.g., rope, wire, vine) perpendicular to the ground The melon should appear to be hanging straight down and the supporting structure should be vertical and should not be curved The supporting structure should be in a straight line pointing directly to the ground", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 808, "Hint": "A hanging melon suspended vertically downwards by a straight supporting structure such as a rope, wire, or vine, with the structure perpendicular to the ground."}, {"Prompt": "The positional relationship between a person and the ground, when there is no gravity", "Explanation": "The model should generate an image where a person is not standing upright on the ground and is not bound by the earth; instead, the person is floating freely in the air and may be at any angle relative to the ground The person’s pose may be oriented in any direction and should not be limited to one direction There is no specific direction for the person in the image The key point of the image is that the person does not obey normal gravity-based orientation to the ground", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 809, "Hint": "A person floating freely in the air, oriented at any angle, with no indication of gravity-based orientation to the ground."}, {"Prompt": "The positional relationship between a pencil and the ground, when there is no gravity", "Explanation": "The model should generate an image where a pencil is not lying on the ground or standing upright Instead, the pencil should be depicted as floating freely in the air, with its direction and angle being arbitrary and not defined by earth's gravity The key aspect is the pencil's state of free floating, irrespective of its alignment with the ground or any particular direction The image should portray the pencil being uninfluenced by gravity", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 810, "Hint": "A pencil floating freely in the air, with its direction and angle arbitrary, uninfluenced by gravity."}, {"Prompt": "A pair and a piece of light wood are placed in a transparent water tank", "Explanation": "The model should generate an image where both the pair and the wood are floating on the surface of the water due to buoyancy", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 811, "Hint": "A pair and a piece of light wood floating on the surface of water in a transparent tank."}, {"Prompt": "A small piece of dry wood and a dense iron block are in a transparent water tank", "Explanation": "The model should generate an image where the piece of wood is floating on the surface of the water, while the iron block is sunk to the bottom. The image should clearly show the difference in buoyancy between the two materials, with one floating and the other completely submerged", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 812, "Hint": "A transparent water tank showing a small piece of dry wood floating on the surface and a dense iron block sunk at the bottom, clearly illustrating the difference in buoyancy."}, {"Prompt": "A tennis ball and a iron block are in a transparent water tank", "Explanation": "The model should generate an image where the tennis ball is floating on the surface of the water, and the iron block is sunk to the bottom of the tank. The image should clearly differentiate between the two objects' positions in water, showing one buoyant and one submerged", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 813, "Hint": "A transparent water tank with a tennis ball floating on the surface and an iron block sunk to the bottom, clearly showing their contrasting positions in the water."}, {"Prompt": "An empty plastic bottle and a large rock in a transparent water tank", "Explanation": "The model should generate an image where the plastic bottle is floating on the surface of the water, while the rock is sunk to the bottom. The image should clearly show the difference in buoyancy between the two materials, with one floating and the other completely submerged", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 814, "Hint": "An image of an empty plastic bottle floating on the water's surface and a large rock sunk to the bottom in a transparent water tank, clearly showing the difference in buoyancy."}, {"Prompt": "A wooden toy boat and several metal screws in a transparent water tank", "Explanation": "The model should generate an image where the wooden toy boat is floating on the surface of the water, while the metal screws are sunk to the bottom of the tank. The image should depict how some objects are buoyant, and some are not, even within the same water tank", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 815, "Hint": "A wooden toy boat floating on the surface of the water and several metal screws sunk to the bottom of a transparent water tank, illustrating buoyancy differences."}, {"Prompt": "A balloon filled with air in a room", "Explanation": "The model should generate an image showing a balloon filled with air resting on the floor or suspended slightly, indicating its lack of significant buoyancy", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 816, "Hint": "A balloon filled with air resting on the floor or suspended slightly in a room."}, {"Prompt": "A balloon filled with helium in a room", "Explanation": "The model should generate an image showing a balloon filled with helium floating towards the ceiling, demonstrating its buoyancy due to being less dense than the surrounding air", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 817, "Hint": "A helium-filled balloon floating near the ceiling of a room."}, {"Prompt": "A bowl of soft dough with a heavy wooden spoon left resting in the center", "Explanation": "The image should depict the spoon's imprint in the dough, indicating the effect of its weight and the pressure exerted on the soft surface", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 818, "Hint": "A bowl of soft dough with a heavy wooden spoon resting in the center, leaving an imprint on the dough."}, {"Prompt": "A laptop resting on a beanbag chair", "Explanation": "The model should generate an image showing the beanbag chair deforming under the pressure of the laptop, conforming to its shape", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 819, "Hint": "A laptop resting on a beanbag chair that is deforming under the pressure, conforming to the laptop's shape."}, {"Prompt": "A stack of plates on a loaf of fresh bread", "Explanation": "The model should generate an image showing the bread being compressed and misshapen due to the pressure of the plates", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 820, "Hint": "A stack of plates compressing and misshaping a loaf of fresh bread."}, {"Prompt": "A glass bottle placed on a pile of soft cotton balls", "Explanation": "The model should generate an image showing the cotton balls being compressed and deformed by the pressure of the bottle", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 821, "Hint": "A glass bottle compressing and deforming a pile of soft cotton balls."}, {"Prompt": "A heavy stone on a block of memory foam", "Explanation": "The model should generate an image displaying the memory foam being indented and compressed by the pressure from the stone", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 822, "Hint": "A heavy stone compressing and indenting a block of memory foam."}, {"Prompt": "A vase of flowers resting on a pile of laundry", "Explanation": "The model should generate an image illustrating the laundry compressed and rumpled from the pressure and weight of the vase", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 823, "Hint": "A vase of flowers resting on compressed and rumpled laundry, showing the pressure and weight of the vase."}, {"Prompt": "A coffee mug on top of a pile of fluffy marshmallows", "Explanation": "The model should generate an image showing the marshmallows being deformed and squashed due to the pressure of the mug", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 824, "Hint": "A coffee mug pressing down on a pile of fluffy marshmallows, causing them to deform and squash under the pressure."}, {"Prompt": "A small toy car sitting on a piece of modeling clay", "Explanation": "The model should generate an image depicting a clear impression or deformation in the clay from the pressure of the toy car’s wheels", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 825, "Hint": "A small toy car sitting on a piece of modeling clay with clear impressions or deformations in the clay from the pressure of the car’s wheels."}, {"Prompt": "A remote control pressing into a mound of whipped cream", "Explanation": "The model should generate an image showing the whipped cream being compressed and deformed by the pressure of the remote", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 826, "Hint": "A remote control pressing into a mound of whipped cream, visibly compressing and deforming the whipped cream."}, {"Prompt": "A full jar of jam sitting on a sponge", "Explanation": "The model should generate an image depicting the sponge compressing under the weight of the jar, showing a clear indentation", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 827, "Hint": "A full jar of jam sitting on a sponge, with the sponge compressing under the weight, showing a clear indentation."}, {"Prompt": "A set of keys placed on a freshly baked cake", "Explanation": "The model should generate an image showing the surface of the cake indented and potentially torn by the pressure of the keys", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 828, "Hint": "A set of keys placed on the surface of a freshly baked cake, with the surface indented and slightly torn by the pressure of the keys."}, {"Prompt": "A beach after many footsteps", "Explanation": "The model should generate an image showing various depths of footprints in the sand, illustrating different levels of pressure applied by walking", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 829, "Hint": "A sandy beach covered with footprints of varying depths, illustrating different levels of pressure applied by walking."}, {"Prompt": "A sealed glass jar in a vacuum chamber", "Explanation": "The model should generate an image showing a glass jar, potentially with its lid slightly bulging outwards, indicating the internal pressure being greater than the external vacuum", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 830, "Hint": "A sealed glass jar with its lid slightly bulging outwards, placed in a vacuum chamber."}, {"Prompt": "An empty plastic bottle under 20 standard atmospheres", "Explanation": "The model should generate an image showing an empty plastic bottle that is visibly crushed or deformed, illustrating the effect of 10 standard atmospheres of external pressure", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 831, "Hint": "An empty plastic bottle that is visibly crushed or deformed, illustrating the effect of 20 standard atmospheres of external pressure."}, {"Prompt": "A small needle carefully placed on the surface of water", "Explanation": "The model should generate an image showing a small needle floating on the surface of water, illustrating how surface tension supports the object despite it being denser than water", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 832, "Hint": "A small needle floating on the surface of water, demonstrating surface tension."}, {"Prompt": "A water strider insect walking on a pond", "Explanation": "The model should generate an image showing a water strider insect supported by the surface of a pond, demonstrating how surface tension allows it to walk on the water without sinking", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 833, "Hint": "A water strider insect walking on the surface of a pond, supported by surface tension."}, {"Prompt": "A single water droplet clinging to a leaf", "Explanation": "The model should generate an image of a water droplet that is perfectly spherical, or nearly so, clinging to a leaf, illustrating surface tension’s effect on the water droplet’s shape", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 834, "Hint": "A perfectly spherical water droplet clinging to a leaf, illustrating surface tension."}, {"Prompt": "A series of small mercury droplets on a smooth glass surface", "Explanation": "The model should generate an image showing several distinct, spherical mercury droplets on a glass surface, exhibiting the strong surface tension of mercury that leads to a minimal contact area", "Category": "Physical Knowledge", "Subcategory": "Mechanics", "prompt_id": 835, "Hint": "Several distinct, spherical mercury droplets on a smooth glass surface, showcasing the strong surface tension of mercury."}, {"Prompt": "Depict a glass of water at a temperature of -10°C, highlighting water's state", "Explanation": "The model should generate an image showing a glass of water, with the water completely frozen into ice due to the -10°C temperature", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 836, "Hint": "A glass of water with the water completely frozen into ice."}, {"Prompt": "Depict a glass of oil at a temperature of -20°C, highlighting oil's state", "Explanation": "The model should generate an image showing a glass of oil with the oil either solidified, or become very viscous and cloudy due to the -20°C temperature", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 837, "Hint": "A glass of oil that is solidified or very viscous and cloudy, representing its state at -20°C."}, {"Prompt": "Depict a glass of water at an arctic environment, highlighting water's state", "Explanation": "The model should generate an image of a glass of water in an arctic environment, with the water completely frozen into ice, suggesting very low temperatures", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 838, "Hint": "A glass of water completely frozen into ice, placed in an arctic environment."}, {"Prompt": "A container of mercury in a freezer, highlighting the state of the mercury", "Explanation": "The model should generate an image of a container with liquid mercury, showing that it is still liquid despite the cold temperatures of the freezer because of its extremely low freezing point", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 839, "Hint": "A container with liquid mercury inside a freezer, showing that the mercury remains liquid despite the cold temperatures."}, {"Prompt": "A pond at minus ten degrees Celsius", "Explanation": "The model should generate an image showing a pond that is frozen, with ice forming on the surface and edges", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 840, "Hint": "A frozen pond with ice forming on the surface and edges."}, {"Prompt": "Depict a glass of water at above one hundred degrees Celsius, highlighting water's state", "Explanation": "The model should generate an image showing a glass of water with the water actively boiling and producing steam, illustrating the effects of being above 100°C", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 841, "Hint": "A glass of water actively boiling and producing steam."}, {"Prompt": "A pot of salted water heated to 100 degrees Celsius", "Explanation": "The model should generate an image showing a pot of salted water, with almost no bubbles and very little steam, reflecting that it is below its boiling point", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 842, "Hint": "A pot of salted water with almost no bubbles and very little steam, reflecting that it is below its boiling point."}, {"Prompt": "The scene of a glass of ethanol at above seventy eight degrees Celsius, highlighting the state of the ethanol", "Explanation": "The model should generate an image showing a glass of ethanol actively boiling, with visible bubbles and vapor, demonstrating its state above 78°C", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 843, "Hint": "A glass of ethanol actively boiling, with visible bubbles and vapor, demonstrating its state above 78°C."}, {"Prompt": "Depict a glass of oil at above 170 degrees Celsius, highlighting oil's state", "Explanation": "The model should generate an image of a glass of oil that shows signs of active boiling and producing steam", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 844, "Hint": "A glass of boiling oil producing steam."}, {"Prompt": "Depict a glass of soda at above 100 degrees Celsius, highlighting soda's state", "Explanation": "The model should generate an image of a glass of soda showing active boiling, with rapid bubbling, producing steam and potentially some splashing or sputtering", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 845, "Hint": "A glass of soda actively boiling with rapid bubbling, producing steam and some splashing or sputtering."}, {"Prompt": "A hot pan on a stove with water droplets on the surface", "Explanation": "The model should generate an image of a hot pan with small droplets of water that are quickly boiling and producing steam, representing rapid vaporization upon contact with a hot surface", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 846, "Hint": "A hot pan on a stove with small droplets of water quickly boiling and producing steam."}, {"Prompt": "A cup of hot tea", "Explanation": "The model should generate an image of a cup of hot tea with clear steam rising, showing the evaporation of water due to its high temperature", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 847, "Hint": "A cup of hot tea with clear steam rising, showing the evaporation of water due to its high temperature."}, {"Prompt": "Depict a glass of ice cubes at above 40 degrees Celsius, highlighting the state of ice cubes", "Explanation": "The model should generate an image showing a glass with ice cubes that are actively melting, with some liquid water present", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 848, "Hint": "A glass with ice cubes actively melting, with some liquid water present."}, {"Prompt": "Depict a glass of butter stick at 70 degrees Celsius, highlighting the butter stick's state", "Explanation": "The model should generate an image showing a butter stick that is in the process of melting, with a soft or partially melted structure due to the 70°C temperature", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 849, "Hint": "A butter stick in a glass, partially melted and soft, indicating the effect of a 70°C temperature."}, {"Prompt": "A chocolate bar left in direct sunlight, highlighting the state of the chocolate", "Explanation": "The model should generate an image showing a chocolate bar that is partially melted and soft, especially along its edges, demonstrating the effect of heat and melting", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 850, "Hint": "A partially melted chocolate bar with soft edges, demonstrating the effect of heat and melting."}, {"Prompt": "A popsicle on a sweltering summer day", "Explanation": "The model should generate an image showing a popsicle that is actively melting, maybe with drips and softened structure", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 851, "Hint": "A melting popsicle with drips and softened structure on a sweltering summer day."}, {"Prompt": "Marshmallow in a bag", "Explanation": "The model should generate an image showing a marshmallow in a bag, retaining its original shape and appearance, suggesting it is at or near room temperature and hasn't been exposed to any extreme heat", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 852, "Hint": "A marshmallow in a bag, retaining its original shape and appearance, suggesting it is at or near room temperature and hasn't been exposed to any extreme heat."}, {"Prompt": "Marshmallow over a bonfire", "Explanation": "The model should generate an image of a marshmallow that is being held over a bonfire with visible signs of melting, charring or expansion, demonstrating its reaction to the intense heat", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 853, "Hint": "A marshmallow being held over a bonfire, with visible signs of melting, charring, or expansion from the intense heat."}, {"Prompt": "A cold soda can left outside on a humid day, highlighting the state of the can", "Explanation": "The model should generate an image showing a cold soda can that is covered with water droplets on its outer surface, demonstrating the effect of condensation due to the contrast between the can's temperature and the humidity of the air", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 854, "Hint": "A cold soda can covered with water droplets on its outer surface, showing condensation due to humid air."}, {"Prompt": "A pair of eyeglasses going from cold to warm, highlighting the state of the lenses", "Explanation": "The model should generate an image of a pair of glasses that are fogged over because of condensation, having been brought from a cold place into a warmer one", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 855, "Hint": "A pair of glasses with fogged-over lenses due to condensation, showing the transition from a cold to a warm environment."}, {"Prompt": "A glass in humid, room-temperature conditions, highlighting the state of the glass surface", "Explanation": "The model should generate an image showing a glass surface covered with a layer of small water droplets", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 856, "Hint": "A glass surface covered with a layer of small water droplets."}, {"Prompt": "A glass door in humid, room-temperature conditions, highlighting the state of the glass door surface", "Explanation": "The model should generate an image showing a glass door that is covered with water droplets, especially near the bottom, where condensation has accumulated due to humidity", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 857, "Hint": "A glass door covered with water droplets, especially near the bottom, where condensation has accumulated due to humidity."}, {"Prompt": "A window in humid, room-temperature conditions, highlighting the state of the window surface", "Explanation": "The model should generate an image showing a window covered in a layer of condensation, with a blurry view outside due to the small water droplets formed on the glass", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 858, "Hint": "A window covered in a layer of condensation with small water droplets on the glass, and a blurry view outside."}, {"Prompt": "The bathroom mirror after a hot shower", "Explanation": "The model should generate an image showing a bathroom mirror that is fogged up with condensation, with water droplets covering the surface due to the increased water vapor from the hot shower", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 859, "Hint": "A fogged-up bathroom mirror covered with water droplets from a hot shower."}, {"Prompt": "The bathroom mirror after a cold shower", "Explanation": "The model should generate an image showing a bathroom mirror that is relatively clear, with minimal or no condensation, indicating that the cold shower did not generate enough water vapor to cause significant condensation", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 860, "Hint": "A bathroom mirror that is relatively clear, with minimal or no condensation, indicating a cold shower lacking significant water vapor."}, {"Prompt": "Freshly poured liquid nitrogen", "Explanation": "The model should generate an image of liquid nitrogen being poured, showing the liquid's extreme coldness through the presence of vaporized nitrogen and condensation on surrounding surfaces", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 861, "Hint": "Liquid nitrogen being poured, with vaporized nitrogen and condensation visible on surrounding surfaces."}, {"Prompt": "Dry ice in a glass of water, highlighting the state of the dry ice and the water", "Explanation": "The model should generate an image showing dry ice submerged in a glass of water with bubbles forming and a foggy vapor being produced, demonstrating rapid sublimation and interaction with the water", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 862, "Hint": "Dry ice submerged in a glass of water with bubbles forming and foggy vapor being produced."}, {"Prompt": "A chunk of dry ice on a hot metal plate, highlighting the state of the dry ice", "Explanation": "The model should generate an image showing a chunk of dry ice placed on a hot metal plate that has a visible vaporous fog immediately coming off of the dry ice due to the rapid sublimation that is caused by the heat of the plate", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 863, "Hint": "A chunk of dry ice placed on a hot metal plate with visible vaporous fog rising due to rapid sublimation caused by the heat."}, {"Prompt": "Depict dry ice under direct sunlight, highlighting the state change of dry ice", "Explanation": "The model should generate an image showing dry ice in sunlight, with a visible plume of vapor or fog rising from the surface, indicating increased sublimation due to the heat from sunlight", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 864, "Hint": "Dry ice under direct sunlight, with a visible plume of vapor or fog rising from the surface, indicating increased sublimation due to heat."}, {"Prompt": "water vapor contacting a chilled glass surface at -10°C, highlighting the surface of glass", "Explanation": "The model should generate an image showing a glass surface at -10°C, covered with a thin layer of frost or ice crystals, illustrating the process of water vapor directly turning into a solid", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 865, "Hint": "A glass surface at -10°C, covered with a thin layer of frost or ice crystals, illustrating the process of water vapor directly turning into a solid."}, {"Prompt": "water vapor contacting a metal plate at -20°C, highlighting the surface of the metal plate", "Explanation": "The model should generate an image showing a metal plate at -20°C with a layer of frost or ice forming directly on the surface, showing the desublimation or deposition of water vapor to ice", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 866, "Hint": "A metal plate at -20°C with a layer of frost or ice forming directly on the surface, showing the deposition of water vapor to ice."}, {"Prompt": "water vapor contacting a window at -10°C, highlighting the surface of the window", "Explanation": "The model should generate an image of a window at -10°C with a layer of frost or intricate ice patterns forming directly on the glass due to deposition of water vapor", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 867, "Hint": "A window at -10°C with a layer of frost or intricate ice patterns forming directly on the glass due to the deposition of water vapor."}, {"Prompt": "A very cold storage container when humid air enters, highlighting the state of the inner surfaces", "Explanation": "The model should generate an image showing the interior surface of a cold storage container that is covered in frost or ice crystals", "Category": "Physical Knowledge", "Subcategory": "Thermodynamics", "prompt_id": 868, "Hint": "The interior surface of a cold storage container covered in frost or ice crystals."}, {"Prompt": "A clear glass of water with a portion of a glass rod submerge", "Explanation": "The model should generate an image showing a clear glass rod partially submerged in a glass of water with the rod appearing to shift or bend at the water’s surface due to light refraction", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 869, "Hint": "A clear glass of water with a glass rod partially submerged, showing the rod appearing to bend at the water’s surface due to light refraction."}, {"Prompt": "A clear glass of water with a portion of a plastic drinking straw submerge", "Explanation": "The model should generate an image showing a plastic straw partially submerged in a glass of water, with the straw appearing to be bent or offset at the water's surface due to light refraction", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 870, "Hint": "A clear glass of water with a partially submerged plastic straw appearing bent or offset at the water's surface due to light refraction."}, {"Prompt": "A clear glass of water with a portion of a pencil submerge", "Explanation": "The model should generate an image showing a pencil partially submerged in a glass of water, with the pencil appearing to be bent or displaced at the water level due to light refraction", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 871, "Hint": "A pencil partially submerged in a clear glass of water, appearing bent or displaced at the water level due to light refraction."}, {"Prompt": "Depict a rainbow", "Explanation": "The model should generate an image showing a rainbow, with a clear arc of colors in the correct order (red, orange, yellow, green, blue, indigo, and violet) from the outer arc to the inner arc, demonstrating the effect of light dispersion by water droplets", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 872, "Hint": "A rainbow with a clear arc of colors in the correct order (red, orange, yellow, green, blue, indigo, and violet) from the outer arc to the inner arc, demonstrating the effect of light dispersion by water droplets."}, {"Prompt": "Light dispersion from a glass prism", "Explanation": "The model should generate an image showing a glass prism with light passing through it and dispersing into a visible spectrum of colors, generally showing the colors from red to violet with a clear separation of colors and with red being closest to the incident light path and violet being furthest from the incident light path demonstrating the concept of light dispersion", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 873, "Hint": "Light passing through a glass prism and dispersing into a visible spectrum of colors, ranging from red to violet, with red closest to the incident light path and violet furthest, demonstrating light dispersion."}, {"Prompt": "Sunlight passing through a crystal, displaying the separated colors", "Explanation": "The model should generate an image of sunlight passing through a crystal, showing the light being broken into its component colors, with those colors generally being in the order of red, orange, yellow, green, blue, indigo, and violet, due to the crystal's light dispersion properties", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 874, "Hint": "Sunlight passing through a crystal, dispersing into a spectrum of colors in the order of red, orange, yellow, green, blue, indigo, and violet."}, {"Prompt": "A light source shining on an oil slick on a wet road, displaying a range of colors", "Explanation": "The model should generate an image showing a light source (like headlights or sunlight) hitting an oil slick on a wet road, with the oil exhibiting a range of rainbow-like colors due to light dispersion and thin-film interference, and generally shows the colors in the correct order from red to violet", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 875, "Hint": "A light source hitting an oil slick on a wet road, showing rainbow-like colors from red to violet due to light dispersion and thin-film interference."}, {"Prompt": "A flashlight beam passing through mist", "Explanation": "The model should generate an image showing a flashlight beam clearly visible as it passes through mist, with the mist illuminated along a bright path due to light scattering", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 876, "Hint": "A flashlight beam illuminating mist, with the mist appearing bright and scattered along the beam's path."}, {"Prompt": "Headlights of a car in heavy fog", "Explanation": "The model should generate an image showing the headlights of a car projecting through thick fog, with a diffused and widespread beam of light, making a bright path through the fog due to the scattering of light", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 877, "Hint": "Headlights of a car projecting through thick fog, with a diffused and widespread beam of light creating a bright path through the fog."}, {"Prompt": "A laser beam passing through a dusty room", "Explanation": "The model should generate an image showing a laser beam that is visible in a dusty room, with a bright path of light scattering off the dust particles, illuminating its path through the room", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 878, "Hint": "A laser beam visible in a dusty room, with a bright path of light scattering off the dust particles, illuminating its path."}, {"Prompt": "Sunlight entering a forest, scattering off the leaves and foliage", "Explanation": "The model should generate an image of sunlight coming through a forest canopy, with a diffuse and scattered light making a bright path through the leaves and foliage due to the light being scattered", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 879, "Hint": "Sunlight coming through a forest canopy, with diffuse and scattered light creating a bright path through the leaves and foliage."}, {"Prompt": "A magnifying glass over a small map", "Explanation": "The model should generate an image of a magnifying glass held over a map, with the map details below the magnifying glass appearing larger than other areas of the map", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 880, "Hint": "A magnifying glass held over a map, with the map details below the magnifying glass appearing larger than other areas of the map."}, {"Prompt": "a magnifying glass placed over an open notebook", "Explanation": "The model should generate an image showing a magnifying glass held above an open notebook, with the text under the lens appearing larger than the text outside of the lens due to magnification", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 881, "Hint": "A magnifying glass held above an open notebook, with the text under the lens appearing larger than the text outside of the lens due to magnification."}, {"Prompt": "Viewing a clock through a handheld magnifier", "Explanation": "The model should generate an image showing a magnifying glass placed over a clock face, with the numbers and hands under the lens appearing larger than those outside the lens, demonstrating magnification", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 882, "Hint": "A clock face viewed through a magnifying glass, with the numbers and hands under the lens appearing larger than those outside the lens."}, {"Prompt": "A magnifying glass over a line of ants walking on the ground", "Explanation": "The model should generate an image showing a magnifying glass above a line of ants with those ants appearing larger under the lens compared to the ones outside of the lens", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 883, "Hint": "A magnifying glass above a line of ants walking on the ground, with the ants appearing larger under the lens compared to those outside of it."}, {"Prompt": "A magnifying glass over a printed circuit board", "Explanation": "The model should generate an image showing a magnifying glass over a printed circuit board, with the small components under the lens appearing larger, demonstrating the magnifying effect", "Category": "Physical Knowledge", "Subcategory": "Optics", "prompt_id": 884, "Hint": "A magnifying glass over a printed circuit board, with small components under the lens appearing larger to demonstrate the magnifying effect."}, {"Prompt": "A cup of oil mixed with water after long time", "Explanation": "The model should generate an image showing a cup where the oil and water have separated into distinct layers, with the oil on top, demonstrating their immiscibility over time and the lower density of oil", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 885, "Hint": "A cup with distinct layers of oil and water, showcasing the oil floating on top due to immiscibility and its lower density."}, {"Prompt": "There is a glass containing water and beer", "Explanation": "The model should generate an image of a glass where the beer is somewhat mixed into the water, showing a less distinct separation than that of oil and water due to the beer’s miscibility", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 886, "Hint": "A glass containing water and beer partially mixed together, showing a less distinct separation due to the beer’s miscibility."}, {"Prompt": "There is a glass containing water and benzene", "Explanation": "The model should generate an image of a glass where the benzene forms a distinct layer above the water. The interface between the two liquids should be clear and well-defined, demonstrating their immiscibility and the lower density of benzene", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 887, "Hint": "A glass containing water with a distinct layer of benzene above it, showing a clear and well-defined interface between the two immiscible liquids."}, {"Prompt": "A soda can opened after violently shaken", "Explanation": "The model should generate an image of a soda can being opened with significant fizzing and a spray of liquid, indicating a large release of pressure due to prior agitation", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 888, "Hint": "A soda can being opened with significant fizzing and a spray of liquid, indicating a large release of pressure due to prior agitation."}, {"Prompt": "A compass needle pointing north near a strong magnet", "Explanation": "The model should generate an image showing a compass needle pointing towards the magnet, even if the magnet isn't perfectly aligned with geographic north, demonstrating magnetic force and interaction", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 889, "Hint": "A compass needle pointing towards a nearby strong magnet, demonstrating magnetic force and interaction."}, {"Prompt": "A tuning fork vibrating inside a glass of water", "Explanation": "The model should generate an image showing a tuning fork vibrating inside a glass of water, with visible ripples or disturbances in the water demonstrating how the vibrations are transferred from the metal to the liquid", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 890, "Hint": "A tuning fork vibrating inside a glass of water, with visible ripples or disturbances in the water demonstrating the transfer of vibrations from the metal to the liquid."}, {"Prompt": "The bulb connected to the battery through tungsten wires", "Explanation": "The model should generate an image of a lightbulb that is lit, connected to a battery by clearly visible tungsten wires, demonstrating good electrical conductivity", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 891, "Hint": "A lit lightbulb connected to a battery with clearly visible tungsten wires."}, {"Prompt": "The bulb connected to the battery through copper wires", "Explanation": "The model should generate an image of a lightbulb that is lit, connected to a battery by clearly visible copper wires, demonstrating good electrical conductivity", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 892, "Hint": "A lit lightbulb connected to a battery with clearly visible copper wires."}, {"Prompt": "The bulb connected to the battery through rubber wires", "Explanation": "The model should generate an image of a lightbulb that is unlit, connected to a battery by clearly visible rubber 'wires,' demonstrating the lack of electrical conductivity of rubber and resulting in a broken circuit", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 893, "Hint": "An unlit lightbulb connected to a battery by clearly visible rubber wires, demonstrating the lack of electrical conductivity and a broken circuit."}, {"Prompt": "A lightbulb connected to a battery with a length of string", "Explanation": "The model should generate an image of a lightbulb that is unlit, connected to a battery using a length of string or thread, demonstrating that string is a poor conductor of electricity", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 894, "Hint": "An unlit lightbulb connected to a battery with a length of string or thread, illustrating that string is a poor conductor of electricity."}, {"Prompt": "A lightbulb connected to a battery by a wooden stick", "Explanation": "The model should generate an image of a lightbulb that is unlit, connected to a battery through a visible wooden stick, demonstrating that wood is a poor conductor of electricity and the circuit will not work", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 895, "Hint": "An unlit lightbulb connected to a battery through a visible wooden stick, demonstrating that wood is a poor conductor of electricity and the circuit will not work."}, {"Prompt": "A transparent beaker containing supersaturated sodium chloride", "Explanation": "The model should generate an image of a clear, transparent solution held in a transparent beaker There may be incipient crystal formation (very small, sparse crystals) visible", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 896, "Hint": "A transparent beaker containing a clear solution with very small, sparse crystals visible inside."}, {"Prompt": "Magnet near iron filings", "Explanation": "The model should generate an image of a magnet with iron filings clinging to it", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 897, "Hint": "A magnet with iron filings clinging to it."}, {"Prompt": "Magnet near cobalt filings", "Explanation": "The model should generate an image of a magnet with cobalt filings clinging to it", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 898, "Hint": "Magnet with cobalt filings clinging to it."}, {"Prompt": "Magnet near nickel filings", "Explanation": "The model should generate an image of a magnet with nickel filings clinging to it", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 899, "Hint": "Magnet with nickel filings clinging to it."}, {"Prompt": "The balloon that has been rubbed comes into contact with long hair", "Explanation": "The model should generate an image of human hair being visibly attracted to a balloon", "Category": "Physical Knowledge", "Subcategory": "Physical Properties", "prompt_id": 900, "Hint": "A balloon visibly attracting human hair."}, {"Prompt": "A candle in space", "Explanation": "The model should generate an image showing a candle that is not burning", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 901, "Hint": "A candle that is not burning in a zero-gravity environment."}, {"Prompt": "Within a sealed glass container of carbon dioxide, a burning candle has been left to stand for a while", "Explanation": "The model should generate an image showing a candle in a sealed glass jar and its flame has been extinguished", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 902, "Hint": "A sealed glass jar containing a candle with its flame extinguished."}, {"Prompt": "A sealed glass jar filled with carbon dioxide contains a burning magnesium rod", "Explanation": "The model should generate an image showing a vigorously burning magnesium rod inside a sealed glass jar. The image should prominently feature a dazzling, intense white light emanating from the burning magnesium", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 903, "Hint": "A sealed glass jar with a vigorously burning magnesium rod emitting a dazzling, intense white light."}, {"Prompt": "A sealed glass jar filled with nitrogen contains a burning magnesium rod", "Explanation": "The model should generate an image showing a burning magnesium rod inside a sealed glass jar, with intense light", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 904, "Hint": "A sealed glass jar filled with nitrogen containing a burning magnesium rod emitting intense light."}, {"Prompt": "A sealed glass jar filled with oxygen contains a burning iron wire", "Explanation": "The model should generate an image showing a thin iron wire inside a sealed glass jar that is burning intensely and producing sparks in a pure oxygen atmosphere, illustrating that oxygen greatly supports combustion", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 905, "Hint": "A sealed glass jar filled with oxygen containing a thin iron wire burning intensely and producing sparks."}, {"Prompt": "A small piece of wood burning in a sealed jar with pure oxygen", "Explanation": "The model should generate an image of a piece of wood that is burning very intensely inside of a jar filled with pure oxygen", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 906, "Hint": "A piece of wood burning very intensely inside a sealed jar filled with pure oxygen."}, {"Prompt": "A lit match inside a sealed jar with only water vapor", "Explanation": "The model should generate an image showing a match that has been extinguished inside of a jar full of only water vapor, with the match looking dark or burnt, and illustrating the lack of oxygen to support combustion", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 907, "Hint": "A burnt match inside a sealed jar filled with water vapor, illustrating the lack of oxygen to support combustion."}, {"Prompt": "A piece of phosphorus burning inside a sealed jar with fluorine gas", "Explanation": "The model should generate an image showing a piece of phosphorus burning rapidly in a sealed jar filled with fluorine gas, with very strong and quick combustion, as fluorine will support even more vigorous combustion than oxygen does", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 908, "Hint": "A piece of phosphorus burning rapidly in a sealed jar filled with fluorine gas, with very strong and quick combustion."}, {"Prompt": "A sodium metal burning inside of a jar of chlorine gas", "Explanation": "The model should generate an image of sodium metal burning in a sealed jar of chlorine gas, with a bright flame and white smoke or particulates", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 909, "Hint": "Sodium metal burning with a bright flame and producing white smoke or particulates in a sealed jar of chlorine gas."}, {"Prompt": "A small candle flame inside of a sealed jar where the air has been replaced with helium", "Explanation": "The model should generate an image of a candle flame that is extinguishing inside of a sealed jar", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 910, "Hint": "A candle flame extinguishing inside a sealed jar."}, {"Prompt": "The sodium is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is distinctly yellow or orange, representing the characteristic flame color when sodium is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 911, "Hint": "A flame that is distinctly yellow or orange, representing the characteristic flame color when sodium is burned."}, {"Prompt": "The copper is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is distinctly green or blue-green, representing the characteristic flame color when copper is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 912, "Hint": "A flame that is distinctly green or blue-green, representing the characteristic flame color when copper is burned."}, {"Prompt": "The potassium is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is distinctly lilac or violet, representing the characteristic flame color when potassium is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 913, "Hint": "A flame burning with a distinct lilac or violet color, representing the characteristic flame of burning potassium."}, {"Prompt": "The lithium is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is distinctly crimson or deep red, representing the characteristic flame color when lithium is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 914, "Hint": "A flame that is distinctly crimson or deep red, representing the characteristic flame color when lithium is burned."}, {"Prompt": "The calcium is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is distinctly orange-red, representing the characteristic flame color when calcium is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 915, "Hint": "An orange-red flame, representing the characteristic color of burning calcium."}, {"Prompt": "The barium is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is distinctly pale green or yellow-green, representing the characteristic flame color when barium is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 916, "Hint": "A flame that is distinctly pale green or yellow-green, representing the characteristic color when barium is burned."}, {"Prompt": "The strontium is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is distinctly crimson or scarlet, representing the characteristic flame color when strontium is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 917, "Hint": "A flame that is distinctly crimson or scarlet, representing the characteristic color when strontium is burned."}, {"Prompt": "The cesium is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is distinctly blue or violet, representing the characteristic flame color when cesium is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 918, "Hint": "A flame that is distinctly blue or violet, representing the characteristic flame color when cesium is burned."}, {"Prompt": "The magnesium is burning, highlighting the color", "Explanation": "The model should generate an image showing a very bright white flame, representing the characteristic flame color when magnesium is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 919, "Hint": "A very bright white flame, representing the characteristic flame color when magnesium is burned."}, {"Prompt": "The aluminum is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is mostly colorless or a very bright white color, with the lack of strong color representing the typical flame color when aluminum is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 920, "Hint": "A flame that is mostly colorless or a very bright white color, representing the typical appearance of burning aluminum."}, {"Prompt": "The zinc is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is mainly colorless or blueish-white to pale green, representing the characteristic flame color when zinc is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 921, "Hint": "A flame that is mainly colorless or blueish-white to pale green, representing the characteristic flame color when zinc is burned."}, {"Prompt": "The lead is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that appears a pale blue or white color, representing the characteristic flame color when lead is burned, which may be faint", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 922, "Hint": "A flame with a pale blue or white color, representing the characteristic flame color when lead is burned."}, {"Prompt": "The antimony is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is pale blue or white, which is the typical color that antimony produces when burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 923, "Hint": "A flame that is pale blue or white, representing burning antimony."}, {"Prompt": "The cadmium is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is red or orange, representing the characteristic flame color when cadmium is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 924, "Hint": "A flame that is red or orange, representing the characteristic flame color when cadmium is burned."}, {"Prompt": "The arsenic is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is pale blue or whitish, representing the characteristic flame color when arsenic is burned, which is typically very faint", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 925, "Hint": "A pale blue or whitish flame, representing the characteristic color when arsenic is burned."}, {"Prompt": "The boron is burning, highlighting the color", "Explanation": "The model should generate an image showing a flame that is bright green, representing the characteristic flame color when boron is burned", "Category": "Chemistry", "Subcategory": "Combustion", "prompt_id": 926, "Hint": "A bright green flame, representing the characteristic color when boron is burned."}, {"Prompt": "an iron block that is not rusted", "Explanation": "The model should generate an image showing an iron block with a clean, metallic, and shiny surface, with no visible signs of rust or corrosion", "Category": "Chemistry", "Subcategory": "Metal Corrosion", "prompt_id": 927, "Hint": "An iron block with a clean, metallic, and shiny surface, with no visible signs of rust or corrosion."}, {"Prompt": "an iron block that is rusted", "Explanation": "The model should generate an image showing an iron block with a surface covered in visible rust, having a reddish-brown color and a rough, pitted texture indicating corrosion", "Category": "Chemistry", "Subcategory": "Metal Corrosion", "prompt_id": 928, "Hint": "An iron block with a surface covered in visible rust, featuring a reddish-brown color and a rough, pitted texture indicating corrosion."}, {"Prompt": "A copper pipe that is rusted", "Explanation": "The model should generate an image of a copper pipe with a visible layer of green patina, showing the characteristic corrosion of copper", "Category": "Chemistry", "Subcategory": "Metal Corrosion", "prompt_id": 929, "Hint": "A copper pipe with a visible layer of green patina, showing the characteristic corrosion of copper."}, {"Prompt": "Copper wire exposed to air for a long time", "Explanation": "The model should generate an image showing a copper wire with a duller, slightly greenish or brownish surface", "Category": "Chemistry", "Subcategory": "Metal Corrosion", "prompt_id": 930, "Hint": "A copper wire with a duller, slightly greenish or brownish surface."}, {"Prompt": "A piece of weathered aluminum", "Explanation": "The model should generate an image showing a piece of aluminum that appears weathered, with a dull grey surface due to the formation of aluminum oxide, rather than rust", "Category": "Chemistry", "Subcategory": "Metal Corrosion", "prompt_id": 931, "Hint": "A piece of aluminum with a dull grey, weathered surface caused by the formation of aluminum oxide."}, {"Prompt": "A lead roof with signs of oxidation", "Explanation": "The model should generate an image of a lead roof with a dull grey or white surface due to the formation of lead oxide, not rust or red, due to oxidation", "Category": "Chemistry", "Subcategory": "Metal Corrosion", "prompt_id": 932, "Hint": "A lead roof with a dull grey or white surface due to the formation of lead oxide from oxidation."}, {"Prompt": "A piece of galvanized steel exposed to moisture, with early signs of corrosion", "Explanation": "The model should generate an image showing a piece of galvanized steel with some white corrosion products forming in spots where the zinc coating is compromised, showing a different type of oxidation to iron", "Category": "Chemistry", "Subcategory": "Metal Corrosion", "prompt_id": 933, "Hint": "A piece of galvanized steel with spots of white corrosion products forming where the zinc coating is compromised, displaying early stages of oxidation."}, {"Prompt": "A piece of silver cutlery that has some tarnish", "Explanation": "The model should generate an image of a piece of silver cutlery, with a dark grey or black tarnish on its surface due to its reaction with sulfur compounds, and not rust", "Category": "Chemistry", "Subcategory": "Metal Corrosion", "prompt_id": 934, "Hint": "A piece of silver cutlery with a dark grey or black tarnish on its surface."}, {"Prompt": "A piece of gold that has been exposed to oxygen for decades", "Explanation": "The model should generate an image of a piece of gold that has no visible corrosion or oxidation, demonstrating that it is highly resistant to oxidation, even after decades of exposure to oxygen", "Category": "Chemistry", "Subcategory": "Metal Corrosion", "prompt_id": 935, "Hint": "A piece of gold with no visible corrosion or oxidation, demonstrating its resistance to oxidation even after decades of exposure to oxygen."}, {"Prompt": "A Gold block submerged in hydrochloric acid", "Explanation": "The model should generate an image showing a gold block in hydrochloric acid with no visible change on the surface of the block, no bubbling, and the solution appearing colorless, demonstrating gold's lack of reactivity with hydrochloric acid", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 936, "Hint": "A gold block submerged in colorless hydrochloric acid with no visible change, no bubbling, and an unchanged surface on the block, demonstrating gold's lack of reactivity."}, {"Prompt": "An iron nail submerged in hydrochloric acid", "Explanation": "The model should generate an image showing an iron nail immersed in hydrochloric acid, with visible bubbling, some corrosion around the nail, and the solution potentially appearing a very pale green or yellow, indicating that it is reacting with the acid", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 937, "Hint": "An iron nail immersed in hydrochloric acid, with visible bubbling, corrosion around the nail, and the solution appearing pale green or yellow, indicating a chemical reaction."}, {"Prompt": "A piece of copper in nitric acid", "Explanation": "The model should generate an image showing a piece of copper in nitric acid, with bubbles forming and brown fumes coming from the solution, and the solution potentially having a blue or green tint as copper ions dissolve into the acid, demonstrating copper reacting with nitric acid", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 938, "Hint": "A piece of copper in nitric acid with bubbles forming, brown fumes rising, and the solution showing a blue or green tint as copper ions dissolve into the acid."}, {"Prompt": "A strip of zinc in sulfuric acid", "Explanation": "The model should generate an image showing a strip of zinc in sulfuric acid with a good amount of bubbling and a colorless and transparent solution during the reaction", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 939, "Hint": "A strip of zinc in a colorless, transparent sulfuric acid solution with visible bubbling during the reaction."}, {"Prompt": "A sample of aluminum in hydrochloric acid", "Explanation": "The model should generate an image of aluminum in hydrochloric acid showing visible bubbling and signs of a reaction, that the aluminum is being dissolved in the acid, and that the solution appears colorless and transparent during this reaction process", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 940, "Hint": "Aluminum in hydrochloric acid with visible bubbling and signs of a reaction; the aluminum is dissolving, and the solution appears colorless and transparent during the reaction."}, {"Prompt": "A piece of magnesium in a solution of hydrochloric acid", "Explanation": "The model should generate an image of a piece of magnesium that is reacting vigorously in hydrochloric acid with the production of many bubbles, indicating that it is rapidly reacting, and the solution appearing colorless and transparent throughout this process", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 941, "Hint": "A piece of magnesium reacting vigorously in a colorless, transparent solution of hydrochloric acid, with many bubbles being produced."}, {"Prompt": "A silver ring placed in nitric acid", "Explanation": "The model should generate an image of a silver ring placed in nitric acid showing no visible reaction or bubbling, and the solution appearing colorless and transparent, indicating silver’s inert nature in that acid", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 942, "Hint": "A silver ring placed in a colorless and transparent nitric acid solution, showing no visible reaction or bubbling."}, {"Prompt": "A platinum wire submerged in hydrochloric acid", "Explanation": "The model should generate an image showing a platinum wire immersed in hydrochloric acid that has no visible bubbling or change, and the solution appearing colorless and transparent, showing its lack of reactivity with this acid", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 943, "Hint": "A platinum wire immersed in a colorless, transparent solution of hydrochloric acid, with no visible bubbling or change."}, {"Prompt": "A piece of tin in dilute sulfuric acid", "Explanation": "The model should generate an image showing a piece of tin in dilute sulfuric acid with visible bubbling, indicating that a chemical reaction is taking place, and the solution appearing colorless and transparent as this reaction occurs", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 944, "Hint": "A piece of tin in a colorless and transparent solution of dilute sulfuric acid with visible bubbling, indicating a chemical reaction."}, {"Prompt": "A piece of nickel being placed in hydrochloric acid", "Explanation": "The model should generate an image showing a piece of nickel in hydrochloric acid where small amounts of bubbling are occurring and the nickel appears to be dissolving very slowly in the acid, demonstrating a slow reaction, with the solution potentially being a very pale green color", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 945, "Hint": "A piece of nickel in hydrochloric acid with small bubbles forming, the nickel slowly dissolving, and the solution appearing very pale green."}, {"Prompt": "A large amount of carbon dioxide is bubbled through clear limewater solution", "Explanation": "The model should generate an image showing a clear limewater solution becoming cloudy or milky as a large amount of carbon dioxide is bubbled through it, due to the formation of a calcium carbonate precipitate", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 946, "Hint": "A clear limewater solution becoming cloudy or milky as a large amount of carbon dioxide is bubbled through it."}, {"Prompt": "A copper sulfate solution and an iron rod in it, the state of the solution and the iron rod should be highlighted", "Explanation": "The model should generate an image showing an iron rod immersed in a blue copper sulfate solution, where the iron rod is corroding, and the blue color of the solution is beginning to fade or change to a light green color, while the surface of the iron rod is covered with a layer of red material demonstrating the reaction between iron and copper sulfate", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 947, "Hint": "An iron rod immersed in a blue copper sulfate solution, with the rod corroding, the blue solution fading to light green, and the rod's surface covered with a layer of red material."}, {"Prompt": "A silver nitrate solution and a zinc bar in it, the state of the solution and the zinc bar should be highlighted", "Explanation": "The model should generate an image showing a zinc bar immersed in a silver nitrate solution where silver metal is forming as a solid, and the zinc bar is being coated with a silvery layer, indicating the reaction between zinc and silver nitrate", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 948, "Hint": "A zinc bar immersed in a silver nitrate solution, with silver metal forming as a solid and the zinc bar coated with a silvery layer, illustrating the reaction between zinc and silver nitrate."}, {"Prompt": "A lead strip in a copper nitrate solution, highlighting the state of the solution and the lead strip", "Explanation": "The model should generate an image of a lead strip in a blue copper nitrate solution where solid copper is beginning to form and plate out on the lead strip, with a reddish-brown color, and the lead metal is appearing corroded and having a duller appearance as it has begun to react in the solution", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 949, "Hint": "A lead strip in a blue copper nitrate solution with solid copper forming as a reddish-brown plating on the lead strip, while the lead appears corroded and dull."}, {"Prompt": "A piece of silver in a solution of copper nitrate, highlighting the state of the solution and the piece of silver", "Explanation": "The model should generate an image of silver in a blue copper nitrate solution, showing no noticeable reaction and no change to either the silver metal or the solution", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 950, "Hint": "A piece of silver in a blue copper nitrate solution, showing no noticeable reaction and no change to either the silver metal or the solution."}, {"Prompt": "A strip of copper in a solution of aluminum sulfate, highlighting the state of the solution and the copper strip", "Explanation": "The model should generate an image of a copper strip in a solution of aluminum sulfate showing no signs of any reaction in the solution or corrosion on the copper", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 951, "Hint": "A strip of copper in a clear solution of aluminum sulfate with no reaction or corrosion visible."}, {"Prompt": "A magnesium strip immersed in a solution of copper chloride, highlighting the state of the solution and the magnesium strip", "Explanation": "The model should generate an image of a magnesium strip reacting rapidly in a solution of copper chloride, with bubbles forming, and the magnesium metal corroding and becoming smaller, and with the copper coming out of solution and visibly plating on the magnesium strip with a reddish-brown color, showing a very rapid reaction", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 952, "Hint": "A magnesium strip reacting rapidly in a solution of copper chloride, with bubbles forming, the magnesium corroding and becoming smaller, and reddish-brown copper plating visibly forming on the magnesium strip."}, {"Prompt": "A piece of iron wire immersed in a nickel sulfate solution, highlighting the state of the solution and the iron wire", "Explanation": "The model should generate an image showing a piece of iron wire in a solution of nickel sulfate, with the wire corroding, and with nickel plating out of solution on the wire with a silvery appearance and the solution's original green color fading or becoming more pale", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 953, "Hint": "A piece of corroding iron wire in a nickel sulfate solution, with silvery nickel plating forming on the wire and the green solution becoming paler."}, {"Prompt": "A strip of tin placed in a silver nitrate solution, highlighting the state of the solution and the tin strip", "Explanation": "The model should generate an image of a strip of tin in a silver nitrate solution where silver metal is visibly forming as a solid, and plating out on the tin strip with a shiny, silvery color and the tin metal is being dissolved and corroding with a pitted or uneven surface, due to the reaction", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 954, "Hint": "A strip of tin in a silver nitrate solution, with silver metal forming as a shiny, silvery solid plating on the tin strip, while the tin strip appears corroded with a pitted, uneven surface."}, {"Prompt": "A nickel bar immersed in a copper chloride solution, highlighting the state of the solution and the nickel bar", "Explanation": "The model should generate an image showing a nickel bar immersed in a green solution of copper chloride, with the nickel slowly dissolving, and a reddish-brown copper layer forming on the surface of the nickel bar, and the solution gradually appearing less green, demonstrating the reaction between copper chloride and nickel, but being slower", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 955, "Hint": "A nickel bar immersed in a green copper chloride solution, with the nickel dissolving slowly, a reddish-brown copper layer forming on the bar's surface, and the solution gradually appearing less green."}, {"Prompt": "A small piece of sodium metal added to water", "Explanation": "The model should generate an image showing a small piece of sodium metal reacting vigorously with water, with visible bubbling, fizzing, and potentially a flame, demonstrating the exothermic reaction", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 956, "Hint": "A small piece of sodium metal reacting vigorously with water, with visible bubbling, fizzing, and a potential flame."}, {"Prompt": "A solution of potassium iodide being mixed with lead nitrate", "Explanation": "The model should generate an image showing two clear solutions being mixed together to create a cloudy or yellow solution with a yellow precipitate, demonstrating a double displacement reaction", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 957, "Hint": "Two clear solutions being mixed together to create a cloudy or yellow solution with a yellow precipitate, demonstrating a double displacement reaction."}, {"Prompt": "Hydrogen sulfide gas is bubbled through a copper sulfate solution", "Explanation": "The model should generate an image showing a clear copper sulfate solution becoming cloudy, and forming a black precipitate as hydrogen sulfide gas is bubbled through it", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 958, "Hint": "A clear copper sulfate solution becoming cloudy and forming a black precipitate as hydrogen sulfide gas is bubbled through it."}, {"Prompt": "Hydrogen sulfide gas is bubbled through a solution of concentrated sulfuric acid", "Explanation": "The model should generate an image showing a concentrated sulfuric acid solution becoming cloudy with visible yellow or white particulate matter (sulfur)", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 959, "Hint": "A concentrated sulfuric acid solution becoming cloudy with visible yellow or white particulate matter (sulfur)."}, {"Prompt": "The process of electrolysis of molten sodium chloride", "Explanation": "The model should generate an image showing an electrolysis setup with molten sodium chloride, producing sodium metal at the cathode, and showing a greenish-yellow gas (chlorine) forming at the anode", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 960, "Hint": "An electrolysis setup with molten sodium chloride, producing sodium metal at the cathode and a greenish-yellow chlorine gas forming at the anode."}, {"Prompt": "The mixture of vinegar and litmus solution in a glass", "Explanation": "The model should generate an image showing a glass of a vinegar and litmus solution mixture, with the liquid appearing red or pink, indicating an acidic solution", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 961, "Hint": "A glass containing a mixture of vinegar and litmus solution, with the liquid appearing red or pink, indicating an acidic solution."}, {"Prompt": "The mixture of cola and litmus solution in a glass", "Explanation": "The model should generate an image of a glass of cola and litmus solution mixed liquid, with the liquid appearing red or pink, indicating an acidic solution", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 962, "Hint": "A glass of cola and litmus solution mixed liquid, with the liquid appearing red or pink, indicating an acidic solution."}, {"Prompt": "The mixture of baking soda solution and litmus solution in a glass", "Explanation": "The model should generate an image showing a glass of baking soda and litmus solution mixture, with the liquid appearing blue or purple, indicating a basic (alkaline) solution", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 963, "Hint": "A glass containing a mixture of baking soda solution and litmus solution, with the liquid appearing blue or purple, indicating a basic (alkaline) solution."}, {"Prompt": "The mixture of milk and litmus solution in a glass", "Explanation": "The model should generate an image of a glass of milk with litmus solution that has been left standing for a while, with the liquid appearing purple or a light blue-purple, indicating a neutral to slightly basic solution", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 964, "Hint": "A glass of milk with litmus solution, appearing purple or light blue-purple, indicating a neutral to slightly basic solution."}, {"Prompt": "A glass of lemon juice and litmus solution mixed liquid that has been standing for a while", "Explanation": "The model should generate an image of a glass containing lemon juice mixed with litmus solution, and the liquid appearing red or pink, indicating an acidic solution", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 965, "Hint": "A glass containing lemon juice mixed with litmus solution, with the liquid appearing red or pink, indicating an acidic solution."}, {"Prompt": "A glass of soapy water and litmus solution mixed liquid that has been standing for a while", "Explanation": "The model should generate an image showing a glass of soapy water mixed with litmus solution, with the solution appearing blue or purple, indicating the basic nature of soapy water", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 966, "Hint": "A glass of soapy water mixed with litmus solution, with the solution appearing blue or purple, indicating the basic nature of soapy water."}, {"Prompt": "A glass of black coffee and litmus solution mixed liquid that has been standing for a while", "Explanation": "The model should generate an image of a glass of black coffee and litmus solution, with the liquid appearing red or pink, indicating an acidic solution", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 967, "Hint": "A glass of black coffee mixed with litmus solution, with the liquid appearing red or pink, indicating an acidic solution."}, {"Prompt": "A glass of ammonia and litmus solution mixed liquid that has been standing for a while", "Explanation": "The model should generate an image of ammonia mixed with litmus solution, with the liquid appearing a dark blue or purple, due to the high basicity of ammonia", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 968, "Hint": "A glass of dark blue or purple liquid, representing ammonia mixed with litmus solution."}, {"Prompt": "A glass of apple juice and litmus solution mixed liquid that has been standing for a while", "Explanation": "The model should generate an image of a glass of apple juice with litmus solution, with the liquid appearing red or pink, indicating that it is an acidic solution", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 969, "Hint": "A glass of apple juice mixed with litmus solution, with the liquid appearing red or pink, indicating acidity."}, {"Prompt": "A glass of hydrochloric acid and phenolphthalein solution mixed liquid that has been standing for a while", "Explanation": "The model should generate an image showing a glass of hydrochloric acid mixed with phenolphthalein solution, with the liquid appearing colorless", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 970, "Hint": "A glass of colorless liquid representing hydrochloric acid mixed with phenolphthalein solution."}, {"Prompt": "A glass of sodium hydroxide solution and phenolphthalein solution mixed liquid that has been standing for a while", "Explanation": "The model should generate an image showing a glass of sodium hydroxide solution mixed with phenolphthalein solution, with the liquid appearing pink or magenta, indicating a basic (alkaline) solution", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 971, "Hint": "A glass of pink or magenta liquid, representing a sodium hydroxide solution mixed with phenolphthalein, indicating a basic (alkaline) solution."}, {"Prompt": "Vinegar after mixing with red cabbage indicator", "Explanation": "The model should generate an image showing a clear solution of vinegar mix with indicator, the solution should look red, indicating its acidity", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 972, "Hint": "A clear red solution, representing vinegar mixed with red cabbage indicator, indicating acidity."}, {"Prompt": "The mixture of baking soda solution and vinegar in a glass", "Explanation": "The model should generate an image showing a glass where a baking soda solution is reacting with vinegar, with visible bubbling due to the release of carbon dioxide gas", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 973, "Hint": "A glass containing a baking soda solution reacting with vinegar, with visible bubbling from the release of carbon dioxide gas."}, {"Prompt": "The mixture of baking soda solution and lemon juice in a glass", "Explanation": "The model should generate an image showing a glass of lemon juice mixed with baking soda, with the mixture fizzing and producing bubbles, due to the formation of carbon dioxide gas", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 974, "Hint": "A glass of lemon juice mixed with baking soda, fizzing and producing bubbles."}, {"Prompt": "A piece of marble reacting with hydrochloric acid", "Explanation": "The model should generate an image of a piece of marble reacting with hydrochloric acid, with visible bubbles being released from the surface of the marble due to carbon dioxide production", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 975, "Hint": "A piece of marble reacting with hydrochloric acid, with visible bubbles being released from the surface of the marble due to carbon dioxide production."}, {"Prompt": "A solution of calcium carbonate reacting with acetic acid", "Explanation": "The model should generate an image showing a solution of calcium carbonate reacting with acetic acid, producing clear bubbles due to the formation of carbon dioxide", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 976, "Hint": "A solution of calcium carbonate reacting with acetic acid, producing clear bubbles due to the formation of carbon dioxide."}, {"Prompt": "Excess hydrochloric acid is added to a cloudy limewater solution", "Explanation": "The model should generate an image showing a cloudy limewater solution becoming clear after excess hydrochloric acid is added, as the calcium carbonate precipitate dissolves due to the acid", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 977, "Hint": "A cloudy limewater solution becoming clear as excess hydrochloric acid is added, dissolving the calcium carbonate precipitate."}, {"Prompt": "Copper sulfate solution mixing with sodium hydroxide solution in a beaker", "Explanation": "The model should generate a image of liquid in a beaker and should show the formation of a light blue precipitate (copper hydroxide)", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 978, "Hint": "A beaker with liquids mixing, showing the formation of a light blue precipitate (copper hydroxide)."}, {"Prompt": "Sodium sulfate solution mixing with barium chloride solution in a beaker", "Explanation": "The model should generate a image of liquid in a beaker, and must show the formation of a white precipitate", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 979, "Hint": "A beaker containing two clear liquids being mixed, with the formation of a white precipitate."}, {"Prompt": "Silver nitrate solution mixing with potassium chromate solution in a test tube", "Explanation": "The model should generate a image of liquid in a test tube, and must show the formation of a red precipitate", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 980, "Hint": "Liquid in a test tube with a red precipitate forming."}, {"Prompt": "Silver nitrate solution mixing with sodium chloride solution in a beaker", "Explanation": "The model should generate a image of liquid in a beaker, and must show the formation of a white precipitate", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 981, "Hint": "A beaker containing two solutions mixing, with a white precipitate forming."}, {"Prompt": "Sulfuric acid stained the T-shirt", "Explanation": "The model should generate an image showing a T-shirt with visible damage and charring in the area where sulfuric acid was spilled, indicating the destructive nature of the acid on organic material", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 982, "Hint": "A T-shirt with visible damage and charring in the area where sulfuric acid was spilled, showing the destructive effect of the acid on organic material."}, {"Prompt": "A piece of wood that has been splashed with concentrated nitric acid", "Explanation": "The model should generate an image showing a piece of wood that has been splashed with concentrated nitric acid with the wood showing signs of burning, discoloration, and degradation, from the acid corrosion", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 983, "Hint": "A piece of wood showing signs of burning, discoloration, and degradation caused by splashes of concentrated nitric acid."}, {"Prompt": "A sheet of paper after concentrated sulfuric acid is poured on it", "Explanation": "The model should generate an image showing a sheet of paper with significant charring, blackening, and degradation in the area where concentrated sulfuric acid was poured, illustrating the destructive and corrosive effect of the acid on organic material", "Category": "Chemistry", "Subcategory": "Solution Chemical Reaction", "prompt_id": 984, "Hint": "A sheet of paper with significant charring, blackening, and degradation in the area where concentrated sulfuric acid was poured, illustrating the destructive and corrosive effect of the acid on organic material."}, {"Prompt": "A laser beam slicing through a glass of colloid", "Explanation": "Image depicts a clear glass or beaker filled with a fluid exhibiting the <PERSON><PERSON><PERSON> effect. A visible laser beam should be shown passing through the fluid, with the path of the beam clearly illuminated within the fluid due to light scattering by colloidal particles. The fluid should appear slightly hazy or cloudy, not completely clear", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 985, "Hint": "A clear glass or beaker filled with a slightly hazy fluid exhibiting the <PERSON><PERSON><PERSON> effect, with a visible laser beam passing through the fluid and its path illuminated due to light scattering by colloidal particles."}, {"Prompt": "A molecule of methane", "Explanation": "The model should generate an image depicting a ball-and-stick or space-filling model of a methane molecule, showing one carbon atom bonded to four hydrogen atoms, to show a simple organic molecule", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 986, "Hint": "A ball-and-stick or space-filling model of a methane molecule, showing one carbon atom bonded to four hydrogen atoms."}, {"Prompt": "Unused charcoals", "Explanation": "The model should generate an image showing unused charcoal, with the pieces appearing dark black, solid, and retaining their original shape and structure, representing the state before combustion", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 987, "Hint": "Unused charcoal pieces, dark black, solid, and retaining their original shape and structure, representing the state before combustion."}, {"Prompt": "Used charcoals", "Explanation": "The model should generate an image showing used charcoal, with pieces that appear white or light grey, broken, indicating that they have undergone combustion and the original carbon source has been consumed", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 988, "Hint": "Used charcoal pieces that are white or light grey, broken, and show signs of combustion with the original carbon source consumed."}, {"Prompt": "Much Salt have been added into the protein solution in a beaker", "Explanation": "The model should generate a beaker, and the solution in it should appear cloudy or have a visible precipitate of the protein", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 989, "Hint": "A beaker containing a solution that appears cloudy or has a visible precipitate of protein."}, {"Prompt": "A burning matchstick dipped into water", "Explanation": "The model should generate an image showing a burning matchstick that is quickly extinguished after being dipped into water, with the charred part of the matchstick appearing darker or blacker due to the water and the extinguishing process", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 990, "Hint": "A burning matchstick being extinguished in water, with the charred part appearing darker or blacker."}, {"Prompt": "White sugar crystals", "Explanation": "The model should generate an image showing pure, white sugar crystals with a clear, well-defined crystalline structure", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 991, "Hint": "Pure, white sugar crystals with a clear, well-defined crystalline structure."}, {"Prompt": "Burnt sugar", "Explanation": "The model should generate an image showing burnt sugar with a dark brown or black color, a sticky or caramelized texture, and possibly smoke or fumes", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 992, "Hint": "Burnt sugar with a dark brown or black color, a sticky or caramelized texture, and possibly smoke or fumes."}, {"Prompt": "Ammonium nitrate crystals dissolving in water in a beaker", "Explanation": "The model should generate an image of water in a beaker with ammonium nitrate crystals being added and dissolving. The image should suggest a decrease in temperature, with condensation forming on the outside of the beaker", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 993, "Hint": "Water in a beaker with ammonium nitrate crystals dissolving, showing condensation forming on the outside of the beaker to suggest a decrease in temperature."}, {"Prompt": "Sodium hydroxide pellets dissolving in water in a beaker", "Explanation": "The model should generate an image of water in a beaker with sodium hydroxide  pellets being added and dissolving, with slight wisps of steam rising from the solution, demonstrating the released heat", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 994, "Hint": "Sodium hydroxide pellets dissolving in water in a beaker, with slight wisps of steam rising from the solution."}, {"Prompt": "A clear solution of copper sulfate", "Explanation": "The model should generate an image showing a transparent, bright blue solution in a glass container, representing dissolved copper sulfate", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 995, "Hint": "A transparent, bright blue solution in a glass container, representing dissolved copper sulfate."}, {"Prompt": "A solution of silver nitrate before light exposure", "Explanation": "The model should generate an image depicting a clear, colorless liquid in a transparent container. There should be no visible precipitate or cloudiness, representing a stable silver nitrate solution protected from light", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 996, "Hint": "A clear, colorless liquid in a transparent container with no visible precipitate or cloudiness, representing a stable silver nitrate solution protected from light."}, {"Prompt": "A solution of silver nitrate after light exposure", "Explanation": "The model should generate an image depicting a solution in transparent container with the silver particles on the bottom. This showcases a container that was not covered or not shielded from radiation", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 997, "Hint": "A solution in a transparent container with visible silver particles settled at the bottom, representing a silver nitrate solution exposed to light."}, {"Prompt": "A solid sample of potassium permanganate", "Explanation": "The model should generate an image showing dark purple or almost black crystals or powder, characteristic of solid potassium permanganate", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 998, "Hint": "Dark purple or almost black crystals or powder, characteristic of solid potassium permanganate."}, {"Prompt": "Potassium permanganate dissolved in water", "Explanation": "The model should generate an image showing a deep purple or magenta solution, representing potassium permanganate dissolved in water. The intensity of the color should indicate a significant concentration", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 999, "Hint": "A deep purple or magenta solution in a container, representing potassium permanganate dissolved in water with a significant concentration."}, {"Prompt": "An open container of a volatile organic solvent", "Explanation": "The model should generate an image showing an open container holding a liquid, with visible vapors emanating from the surface. The scene should convey the rapid evaporation of the solvent and potential hazards associated with its flammability or inhalation", "Category": "Chemistry", "Subcategory": "Chemical Properties", "prompt_id": 1000, "Hint": "An open container holding a liquid with visible vapors emanating, conveying rapid evaporation and potential hazards like flammability or inhalation."}, {"Prompt": "The Sydney Opera House when it's 8 AM in San Francisco", "Explanation": "The model should show the Sydney Opera House in the evening, as it's ahead in time compared to San Francisco. The lights inside the building might be on, and there could be people leaving after an evening performance or event.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 401, "Hint": "The Sydney Opera House in the evening with lights inside the building and people leaving after an evening performance or event."}, {"Prompt": "The mountain trail when the skiers are coming down.", "Explanation": "The model should show the mountain trail covered in snow with skiers skiing down.  This indicates that it is winter, and the trail is used for skiing activities.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 402, "Hint": "Mountain trail covered in snow with skiers skiing down."}, {"Prompt": "The wheat field when the frogs croak loudly at night.", "Explanation": "The model should generate an image of a ripe wheat field.    Frogs croak loudly at night in summer, and wheat is ripe in summer.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 403, "Hint": "A ripe wheat field on a summer night."}, {"Prompt": "The school playground when the leaves are falling from the trees.", "Explanation": "A picture of the school playground with fallen leaves scattered on the ground and the trees with fewer leaves should be generated.  Falling leaves indicate that it is autumn, and the playground should have an autumnal atmosphere.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 404, "Hint": "A school playground with fallen leaves scattered on the ground, trees with fewer leaves, and an autumnal atmosphere."}, {"Prompt": "The mountain when the leaves are turning red and the birds are migrating south.", "Explanation": "The model should generate an image of a mountain with red leaves.    Leaves turning red indicates it's autumn, and birds migrating south also shows that it's autumn.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 405, "Hint": "A mountain in autumn with red leaves and birds migrating south."}, {"Prompt": "The persimmon tree when farmers begin harvesting sweet potatoes.", "Explanation": "Persimmons should be orange and ripe.  Sweet potato harvest coincides with autumn when persimmons mature.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 406, "Hint": "A persimmon tree with ripe, orange persimmons in an autumn setting."}, {"Prompt": "The owls when the moon is rising", "Explanation": "An image of owls perched on tree branches should be generated, as owls are nocturnal and become active when the moon rises.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 407, "Hint": "Owls perched on tree branches under a rising moon."}, {"Prompt": "A park in London at 10 PM during the summer solstice", "Explanation": "The image should depict a park in London with the sky still relatively light due to the late sunset on the summer solstice. There might be people still enjoying the outdoors, having picnics or just relaxing on the benches, as the day is longer at this time of the year.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 408, "Hint": "A park in London with a relatively light sky at 10 PM during the summer solstice, showing people enjoying picnics or relaxing on benches."}, {"Prompt": "The sunflowers when the sun is setting", "Explanation": "The model should generate an image of sunflowers facing west, as sunflowers track the sun during the day and face west when the sun is setting.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 409, "Hint": "Sunflowers facing west at sunset."}, {"Prompt": "The river when the squirrels are busy storing food.", "Explanation": "The model should generate an image of a frozen river.    Squirrels store food in autumn or approaching winter, and rivers freeze in winter.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 410, "Hint": "A frozen river in winter."}, {"Prompt": "The fishing village when sardine boats stop going out at night.", "Explanation": "Village docks empty. Night fishing stops when sardines migrate away in winter.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 411, "Hint": "Empty village docks at night during winter, with no sardine boats present."}, {"Prompt": "The street lamps when the first fireflies appear in the meadow.", "Explanation": "The image should show illuminated street lamps with fireflies glowing.  Fireflies typically appear at dusk, so the scene should have twilight lighting.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 412, "Hint": "Illuminated street lamps at twilight with glowing fireflies in a meadow."}, {"Prompt": "The Pyramids of Giza at 8 PM Tokyo time.", "Explanation": "The image should depict the Pyramids of Giza in the early afternoon, with tourists and clear daylight.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 413, "Hint": "The Pyramids of Giza in the early afternoon, with tourists and clear daylight."}, {"Prompt": "The wheat field when the frogs are croaking loudly at night.", "Explanation": "A picture of a wheat field that is green and growing should be generated.  Frogs croaking loudly at night suggests that it is spring or early summer, and wheat is usually growing during this time.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 414, "Hint": "A green and growing wheat field on a spring or early summer night."}, {"Prompt": "The rice paddies when lotus flowers start closing their petals.", "Explanation": "Paddies flooded with young rice plants.  Lotuses close at dusk, indicating evening time.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 415, "Hint": "Rice paddies flooded with young rice plants at dusk, with lotus flowers starting to close their petals."}, {"Prompt": "The cherry trees when beekeepers open their hives for first harvest.", "Explanation": "Cherry blossoms in full bloom.  Beekeepers typically harvest honey in spring during major nectar flows.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 416, "Hint": "Cherry blossoms in full bloom during spring with beekeepers harvesting honey nearby."}, {"Prompt": "The Sydney Opera House at 6 PM London Time.", "Explanation": "The image should depict Sydney Opera House in the early morning, possibly with sunrise colors.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 417, "Hint": "Sydney Opera House during sunrise with warm morning colors."}, {"Prompt": "A Rio de Janeiro beach at 9 AM Moscow time.", "Explanation": "The image should depict a Rio beach at early night, likely empty or with very few people.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 418, "Hint": "A Rio de Janeiro beach at early night, likely empty or with very few people."}, {"Prompt": "The maple syrup buckets when wood frogs start croaking.", "Explanation": "Buckets should be actively collecting sap. Wood frogs breed in early spring when maple sap flows.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 419, "Hint": "Buckets actively collecting maple sap in early spring with wood frogs croaking nearby."}, {"Prompt": "The cranberry bog when geese form V-shaped flocks.", "Explanation": "Bogs flooded for harvest (red berries visible). Geese migrate south during autumn harvest.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 420, "Hint": "A cranberry bog flooded for harvest with visible red berries, featuring geese flying in V-shaped flocks during autumn."}, {"Prompt": "The street when the streetlights are turned on and the shops are closing.", "Explanation": "A picture of the street in the evening with streetlights illuminating the area and shop shutters being closed should be generated.  This indicates that it is getting late in the evening, and the street should have a quieter and dimly - lit atmosphere.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 421, "Hint": "A quiet street in the evening with streetlights illuminating the area and shop shutters being closed, creating a dimly lit atmosphere."}, {"Prompt": "A busy street in Tokyo at midnight local time", "Explanation": "An image of a street in Tokyo with neon lights still bright, but with fewer pedestrians and maybe some late - night food stalls still open. The atmosphere should reflect the quietness of the city at midnight compared to its daytime hustle and bustle.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 422, "Hint": "A street in Tokyo at midnight with bright neon lights, fewer pedestrians, and some late-night food stalls open, capturing the quieter atmosphere compared to daytime."}, {"Prompt": "The garden when the butterflies are fluttering around the flowers.", "Explanation": "An image of a garden with colorful flowers in full bloom and butterflies flying around should be generated.  Butterflies are active during spring and summer when flowers are blooming.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 423, "Hint": "A garden with colorful flowers in full bloom and butterflies flying around during spring or summer."}, {"Prompt": "The wheat field when children start making dandelion chains.", "Explanation": "The wheat should be golden and ready for harvest.  Dandelion chains are made in late spring/early summer when both dandelions bloom and wheat matures.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 424, "Hint": "A golden wheat field ready for harvest with blooming dandelions nearby."}, {"Prompt": "The lighthouse when monarch butterflies cluster on pine branches.", "Explanation": "Lighthouse beam visible in autumn dusk. Monarchs migrate south in fall when lighthouse use increases.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 425, "Hint": "Lighthouse beam visible in autumn dusk surrounded by monarch butterflies clustering on pine branches."}, {"Prompt": "A desert landscape in Sahara when it's 11 PM in London", "Explanation": "An image of the Sahara desert at night, with the sky full of stars and the temperature much cooler than during the day. There might be some desert animals active at night, like scorpions or snakes, and the sand dunes should be in darkness except for the light of the moon.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 426, "Hint": "A nighttime scene of the Sahara desert with sand dunes under the light of the moon, a star-filled sky, and possible sightings of nocturnal desert animals like scorpions or snakes."}, {"Prompt": "The daffodils when the birds return from migration.", "Explanation": "The model should generate an image of blooming daffodils.    Birds return from migration in spring, and daffodils bloom in spring.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 427, "Hint": "Blooming daffodils in spring with birds in the background."}, {"Prompt": "A fishing boat in the Mediterranean Sea at 5 AM Chicago time.", "Explanation": "The image should depict a fishing boat in the Mediterranean Sea in the late morning/early afternoon, with clear skies and bright sunlight.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 428, "Hint": "A fishing boat in the Mediterranean Sea in the late morning or early afternoon, with clear skies and bright sunlight."}, {"Prompt": "A Diwali celebration in India at 10 AM New York time.", "Explanation": "The image should depict a Diwali celebration in India at night, with lit lamps and fireworks.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 429, "Hint": "A Diwali celebration in India at night, with lit lamps and fireworks."}, {"Prompt": "A coffee plantation in Colombia at 8 AM Dubai time.", "Explanation": "The image should depict a coffee plantation in Colombia at late night", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 430, "Hint": "A coffee plantation in Colombia at late night."}, {"Prompt": "The cactus garden when hummingbird feeders are taken down.", "Explanation": "Cacti dormant (no flowers). Feeders removed when hummingbirds migrate in fall.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 431, "Hint": "A cactus garden with dormant cacti, no flowers, and no visible hummingbird feeders."}, {"Prompt": "The fishing boats when cherry blossoms cover the river surface.", "Explanation": "Boats should have fishing nets stored (off-season). Cherry blossom fall marks spring when fishing moratorium begins.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 432, "Hint": "Fishing boats with nets stored on deck, floating on a river covered with fallen cherry blossoms, symbolizing spring and the off-season for fishing."}, {"Prompt": "The city skyline when the morning chorus of robins begins.", "Explanation": "The scene should show predawn lighting with city lights still on.  <PERSON><PERSON>' morning chorus starts before sunrise.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 433, "Hint": "A city skyline under predawn lighting with city lights still on, symbolizing the early morning chorus of robins before sunrise."}, {"Prompt": "A quiet park in Buenos Aires at 2 PM Cairo time.", "Explanation": "The image should depict a quiet park in Buenos Aires in the late evening, with minimal activity.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 434, "Hint": "A quiet park in Buenos Aires in the late evening, with minimal activity."}, {"Prompt": "A ski resort in the Swiss Alps at 7 AM Tokyo time.", "Explanation": "The image should depict a ski resort in the Swiss Alps in the late night, with few people", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 435, "Hint": "A ski resort in the Swiss Alps during the late night with few people."}, {"Prompt": "The Statue of Liberty at 10 PM Dubai time.", "Explanation": "The image should depict the Statue of Liberty in the early afternoon, with clear skies and bustling activity.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 436, "Hint": "The Statue of Liberty in the early afternoon, with clear skies and bustling activity."}, {"Prompt": "The Tokyo market at 3 PM London time.", "Explanation": "The image should depict a Tokyo market in the late evening or night, not in the afternoon.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 437, "Hint": "A Tokyo market in the late evening or night."}, {"Prompt": "A university campus in London at 1 AM Melbourne time.", "Explanation": "The image should depict a university campus in London at around mid-morning, with students arriving for classes.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 438, "Hint": "A university campus in London during mid-morning, with students arriving for classes."}, {"Prompt": "The Great Wall of China when it's 3 PM in Los Angeles", "Explanation": "The model should generate a scene of the Great Wall of China in the early morning hours, considering the time difference. The sun should be just rising, and the wall should be bathed in the soft light of dawn, with few tourists around.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 439, "Hint": "The Great Wall of China at dawn with the sun rising, bathed in soft light and with few tourists around."}, {"Prompt": "The almond orchard when beekeepers move their hives away.", "Explanation": "Almond trees should be past blooming. Hives are removed after pollination season ends.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 440, "Hint": "An almond orchard with trees past blooming and no beehives visible."}, {"Prompt": "The riverbank when the salmon are swimming upstream to spawn.", "Explanation": "The model should create a scene of the riverbank with salmon swimming upstream.  This usually happens in late summer or autumn, and the riverbank should have an autumnal look with fallen leaves and a cooler atmosphere.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 441, "Hint": "A riverbank in autumn with salmon swimming upstream to spawn, surrounded by fallen leaves and a cool atmosphere."}, {"Prompt": "The view of Tiananmen Square at 12 PM New York time", "Explanation": "The model should generate an image of Tiananmen Square at night", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 442, "Hint": "Tiananmen Square at night."}, {"Prompt": "A street market in Marrakech at 8 PM Tokyo time.", "Explanation": "The image should depict a street market in Marrakech in the early afternoon, bustling with shoppers and vendors.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 443, "Hint": "A bustling street market in Marrakech in the early afternoon, with shoppers and vendors."}, {"Prompt": "The cherry orchard when floodlights illuminate the trees at night.", "Explanation": "Cherry trees bare. Night illumination used in early spring to delay blooming.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 444, "Hint": "Cherry trees bare, illuminated by floodlights at night in early spring."}, {"Prompt": "The coastal pine forest when sea turtles begin laying eggs.", "Explanation": "Pine trees with mature cones.  Sea turtles nest in summer when pines complete pollination.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 445, "Hint": "A coastal pine forest with mature cones during summer, with sea turtles nesting on the nearby beach."}, {"Prompt": "The Eiffel Tower when the sun rises in Paris", "Explanation": "The model should generate an image of the Eiffel Tower with the early morning sunlight just starting to shine on it, casting long shadows. The sky should have the colors of dawn, and there might be a few people starting their day around the tower.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 446, "Hint": "The Eiffel Tower with the early morning sunlight shining on it, casting long shadows, under a dawn sky with soft colors and a few people starting their day nearby."}, {"Prompt": "A busy New York street at 3 AM Sydney time.", "Explanation": "The image should show a New York street during the mid-afternoon, likely bustling with activity.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 447, "Hint": "A busy New York street during the mid-afternoon."}, {"Prompt": "The alpine meadow when marmots begin fattening for hibernation.", "Explanation": "Meadow flowers in full bloom (summer).  Marmots prepare for hibernation in late summer.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 448, "Hint": "An alpine meadow in late summer with flowers in full bloom and marmots fattening for hibernation."}, {"Prompt": "The apple orchard when the farmers are harvesting pumpkins.", "Explanation": "An image of an apple orchard with apples still on the trees but starting to change color should be generated.  Harvesting pumpkins indicates that it is autumn, and apples are usually harvested in late summer or early autumn, so there should still be some apples left on the trees.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 449, "Hint": "An apple orchard with apples still on the trees, starting to change color, set in an autumn landscape where farmers are harvesting pumpkins."}, {"Prompt": "The Great Wall of China at 4 PM Dubai time.", "Explanation": "The image should depict the Great Wall of China at night, with few to no people.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 450, "Hint": "The Great Wall of China at night with few to no people."}, {"Prompt": "The view of The White House at 12 AM Beijing time", "Explanation": "The model should generate an image of The White House at night", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 451, "Hint": "The White House at night."}, {"Prompt": "A rainforest in the Amazon when it's 5 AM in New York", "Explanation": "The image should depict the Amazon rainforest in the middle of the night, with the sounds of nocturnal animals echoing through the trees. The moonlight might filter through the dense canopy, illuminating some parts of the forest floor.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 452, "Hint": "Amazon rainforest at night with moonlight filtering through the dense canopy, illuminating parts of the forest floor, and nocturnal animals active."}, {"Prompt": "The Great Wall of China at 3 PM Dubai time.", "Explanation": "The image should depict the Great Wall of China in the late evening or at night, with few to no people.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 453, "Hint": "The Great Wall of China in the late evening or at night, with few to no people."}, {"Prompt": "The wheat field when the swallows build nests.", "Explanation": "A picture of a wheat field with golden - colored wheat that is about to mature should be generated. Swallows build nests in spring, and wheat grows and ripens in spring and early summer.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 454, "Hint": "A golden wheat field with mature wheat under a bright spring or early summer sky, with swallows flying nearby, symbolizing nesting season."}, {"Prompt": "A coffee plantation in Colombia at 7 AM Dubai time.", "Explanation": "The image should depict a coffee plantation in Colombia at late night or pre-dawn, likely quiet and dimly lit.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 455, "Hint": "A quiet, dimly lit coffee plantation in Colombia during the late night or pre-dawn hours."}, {"Prompt": "The Eiffel Tower at 8 PM Tokyo time.", "Explanation": "The image should depict the Eiffel Tower in the early afternoon, with tourists and clear daylight.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 456, "Hint": "The Eiffel Tower in the early afternoon, surrounded by tourists and under clear daylight."}, {"Prompt": "The desert cactus when roadrunners start building nests.", "Explanation": "Cactus should be blooming.  Roadrunners nest in spring when desert plants flower.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 457, "Hint": "A blooming desert cactus with roadrunners building nests in spring."}, {"Prompt": "The city square when the fireflies are glowing.", "Explanation": "The model should create a scene of the city square at night with fireflies glowing around.  Fireflies are active during warm summer nights, so the square should be depicted in a summer nighttime setting.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 458, "Hint": "A city square at night during summer with fireflies glowing around."}, {"Prompt": "A Christmas market in Berlin at 2 PM Sydney time.", "Explanation": "The image should depict a Christmas market in Berlin at night, with festive lights and snow.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 459, "Hint": "A Christmas market in Berlin at night, with festive lights and snow."}, {"Prompt": "A Carnival parade in Rio de Janeiro at 10 PM London time.", "Explanation": "The image should depict a Carnival parade in Rio de Janeiro in the late afternoon, with the bright sun shining.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 460, "Hint": "A Carnival parade in Rio de Janeiro in the late afternoon, with the bright sun shining."}, {"Prompt": "The maple syrup buckets when sap starts dripping from birch trees.", "Explanation": "Maple buckets should be empty.  Birch sap runs after maple season (later in spring).", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 461, "Hint": "Empty maple syrup buckets hanging on birch trees during late spring."}, {"Prompt": "A famous landmark in Paris at 4 AM Los Angeles time", "Explanation": "The image should depict a famous landmark in Paris in the early afternoon, possibly with tourists.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 462, "Hint": "Eiffel Tower in the early afternoon, possibly with tourists."}, {"Prompt": "The beach when the children are playing with snowmen.", "Explanation": "The model should show a beach scene with snow covering the ground and children building snowmen.  This implies that it is winter, and the beach is covered in snow, which is an unusual but possible scenario in some cold regions during winter.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 463, "Hint": "A snowy beach scene with children building snowmen."}, {"Prompt": "The beach in Sydney when it's 6 AM New York time", "Explanation": "The model should create a picture of the beach in Sydney in the middle of the night, as there is a significant time difference between Sydney and New York. It should show the dark sky, the quiet sea, and maybe some stars still visible.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 464, "Hint": "The beach in Sydney at night with a dark sky, a quiet sea, and visible stars."}, {"Prompt": "The sunflower field when crickets start chirping at dusk.", "Explanation": "Sunflowers should be in full bloom. Cricket choruses peak in late summer when sunflowers mature.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 465, "Hint": "A field of sunflowers in full bloom at dusk, symbolizing late summer."}, {"Prompt": "A coffee shop in Rome at 10 AM Chicago time.", "Explanation": "The image should depict a coffee shop in Rome in the late afternoon/early evening, with people relaxing or enjoying a late snack", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 466, "Hint": "A coffee shop in Rome in the late afternoon/early evening, with people relaxing or enjoying a late snack."}, {"Prompt": "The beach when the crabs are molting.", "Explanation": "The model should generate an image of a relatively empty beach with few tourists.    Crabs molt in late spring or early summer, and beaches are not so crowded at that time compared to midsummer.", "Category": "time", "Subcategory": "Horizontal time", "prompt_id": 467, "Hint": "A relatively empty beach with few tourists in late spring or early summer."}, {"Prompt": "The type of weapon used by knights in the medieval times", "Explanation": "The model should generate an image of a mace or a flail", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 468, "Hint": "A mace or a flail used by knights in medieval times."}, {"Prompt": "A beach during winter.", "Explanation": "The image should depict a deserted beach with cold colors, perhaps with some snow or ice, and no people.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 469, "Hint": "A deserted beach with cold colors, some snow or ice, and no people."}, {"Prompt": "The primary mode of long-distance communication in the early 19th century", "Explanation": "The model should generate an image of a stagecoach delivering mail", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 470, "Hint": "A stagecoach delivering mail."}, {"Prompt": "A specific type of sword used by medieval European knights", "Explanation": "The model should generate an image of a broadsword or a longsword", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 471, "Hint": "Broadsword or longsword."}, {"Prompt": "The musical instrument that became a symbol of jazz music in the 1920s", "Explanation": "The model should generate an image of a saxophone", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 472, "Hint": "Saxophone."}, {"Prompt": "The symbolic handheld device carried by affluent men in the 18th century", "Explanation": "The model should generate an image of a walking cane or a decorative snuff box", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 473, "Hint": "Walking cane or decorative snuff box."}, {"Prompt": "The form of entertainment dominant before the invention of television in the early 20th century", "Explanation": "The model should generate an image of a stage play or a vaudeville performance", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 474, "Hint": "A stage play or a vaudeville performance."}, {"Prompt": "The popular style of shoe worn by women in the 1950s", "Explanation": "The model should generate an image of stiletto heels", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 475, "Hint": "Stiletto heels."}, {"Prompt": "A lavender field during summer.", "Explanation": "The image should depict a field of lavender with fully bloomed purple flowers.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 476, "Hint": "A field of lavender with fully bloomed purple flowers during summer."}, {"Prompt": "The specific style of clothing worn by samurais during the Edo period", "Explanation": "The model should generate an image of a Samurai wearing traditional armor like an O-yoroi or a Katana", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 477, "Hint": "A Samurai wearing traditional armor like an O-yoroi and holding a Katana."}, {"Prompt": "A close-up of a maple leaf in summer.", "Explanation": "The image should show a green maple leaf, without any hints of red, orange, or yellow. As the colors of autumn would not be found in summer.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 478, "Hint": "A close-up of a green maple leaf."}, {"Prompt": "The style of armor worn by Greek hoplites in ancient times", "Explanation": "The model should generate an image of hoplite armor, including a bronze cuirass, helmet, and greaves", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 479, "Hint": "Hoplite armor, including a bronze cuirass, helmet, and greaves."}, {"Prompt": "The specific type of printing press used by <PERSON><PERSON><PERSON> in the 15th century", "Explanation": "The model should generate an image of a Gutenberg printing press, with its screw and movable type", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 480, "Hint": "Gutenberg printing press with its screw and movable type."}, {"Prompt": "The specific form of currency used in ancient Rome", "Explanation": "The model should generate an image of a Roman denarius coin", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 481, "Hint": "Roman denarius coin."}, {"Prompt": "A lavender field during winter.", "Explanation": "The image should show a field of lavender covered with snow or with dried brown stems, without any flowers.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 482, "Hint": "A lavender field covered with snow or with dried brown stems, without any flowers."}, {"Prompt": "A wheat field during late autumn.", "Explanation": "The image should show a wheat field with golden-brown stalks ready for harvest, or with stubble after harvesting.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 483, "Hint": "A wheat field with golden-brown stalks ready for harvest or with stubble after harvesting."}, {"Prompt": "The hairstyle worn by Chinese men in the late 17th century", "Explanation": "The model should generate an image of a traditional Manchu queue hairstyle, where the hair is shaved in the front and tied into a long braid in the back", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 484, "Hint": "Traditional Manchu queue hairstyle, with the hair shaved in the front and tied into a long braid in the back."}, {"Prompt": "A specific type of camera used in the 19th century for early photography", "Explanation": "The model should generate an image of a large, wooden box camera with a bellows", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 485, "Hint": "A large, wooden box camera with a bellows."}, {"Prompt": "The unique style of sandal worn by ancient Romans", "Explanation": "The model should generate an image of a Roman caligae sandal with its thick sole and crisscrossed straps", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 486, "Hint": "Roman caligae sandal with a thick sole and crisscrossed straps."}, {"Prompt": "A deciduous forest in the fall", "Explanation": "The image should depict a forest with trees displaying autumn foliage, with red, yellow, and orange leaves.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 487, "Hint": "A forest with trees displaying autumn foliage in red, yellow, and orange leaves."}, {"Prompt": "A bear during winter.", "Explanation": "The image should depict a bear sleeping in its den, as this is typical of bears during winter hibernation.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 488, "Hint": "A bear sleeping in its den during winter hibernation."}, {"Prompt": "The standard clock found in homes in the late 19th century", "Explanation": "The model should generate an image of a pendulum clock or a mantel clock", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 489, "Hint": "Pendulum clock or mantel clock."}, {"Prompt": "A cotton field during late summer", "Explanation": "The image should depict a cotton field with open white bolls of cotton", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 490, "Hint": "A cotton field with open white bolls of cotton."}, {"Prompt": "The typical hairstyle worn by men in modern society", "Explanation": "The model should generate an image of a man with a short, neatly styled haircut, such as a buzz cut, crew cut, or undercut, representing contemporary men's fashion", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 491, "Hint": "A man with a short, neatly styled haircut, such as a buzz cut, crew cut, or undercut, representing contemporary men's fashion."}, {"Prompt": "A flock of geese near a lake during summer.", "Explanation": "The image should show a flock of geese swimming in a lake, with green vegetation around them.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 492, "Hint": "A flock of geese swimming in a lake surrounded by green vegetation during summer."}, {"Prompt": "The type of dress typically worn by women during the Victorian era", "Explanation": "The model should generate an image of a Victorian era dress with a high neckline, tight waist, and long, full skirt", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 493, "Hint": "Victorian era dress with a high neckline, tight waist, and long, full skirt."}, {"Prompt": "The specific type of armor worn by medieval Japanese warriors", "Explanation": "The model should generate an image of Samurai armor like an o-yoroi or a dō-maru, with specific plates and lacing", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 494, "Hint": "Samurai armor like an o-yoroi or a dō-maru, featuring specific plates and lacing."}, {"Prompt": "The method of creating images on a glass plate used by early photographers before film", "Explanation": "The model should generate an image of a wet plate collodion process or a Daguerreotype setup", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 495, "Hint": "Wet plate collodion process or a Daguerreotype setup."}, {"Prompt": "A beach during summer.", "Explanation": "The image should show a sunny beach with people swimming, sunbathing, and engaging in summer activities.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 496, "Hint": "A sunny beach with people swimming, sunbathing, and engaging in summer activities."}, {"Prompt": "The common form of transportation used by wealthy Europeans in the 18th century", "Explanation": "The model should generate an image of a horse-drawn carriage", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 497, "Hint": "Horse-drawn carriage."}, {"Prompt": "The distinctive shape of a Viking longship used for naval combat", "Explanation": "The model should generate an image of a Viking longship with its long, narrow hull, single mast, and decorative prow", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 498, "Hint": "Viking longship with a long, narrow hull, single mast, and decorative prow."}, {"Prompt": "A cherry blossom tree in autumn.", "Explanation": "The image should depict a cherry tree with bare branches or with fall foliage. Cherry blossoms do not bloom in autumn.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 499, "Hint": "A cherry tree with bare branches or fall foliage in autumn."}, {"Prompt": "The typical garment worn by laborers in the early industrial revolution factories", "Explanation": "The model should generate an image of a worker wearing a simple cotton shirt and trousers, possibly with an apron", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 500, "Hint": "A worker wearing a simple cotton shirt and trousers, possibly with an apron."}, {"Prompt": "A popular square-shaped toy from the 1980s", "Explanation": "The model should generate an image of a Rubik's Cube", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 501, "Hint": "<PERSON><PERSON><PERSON>'s <PERSON><PERSON>."}, {"Prompt": "The signature vehicle associated with the hippie movement in the 1960s", "Explanation": "The model should generate an image of a Volkswagen Bus", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 502, "Hint": "Volkswagen Bus."}, {"Prompt": "A sunflower field during summer.", "Explanation": "The image should depict a field full of sunflowers with large, yellow flower heads facing the sun.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 503, "Hint": "A field full of sunflowers with large, yellow flower heads facing the sun during summer."}, {"Prompt": "The household tool that revolutionized laundry day in the 19th century", "Explanation": "The model should generate an image of a mechanical washing machine with a hand-crank or early motor", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 504, "Hint": "A mechanical washing machine with a hand-crank or early motor."}, {"Prompt": "The primary form of transportation used in ancient Rome", "Explanation": "The model should generate an image of a chariot", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 505, "Hint": "Chariot."}, {"Prompt": "The most iconic haircut that characterized the women's fashion trend of the 1920s", "Explanation": "The model should generate an image of a bob haircut, short and often styled with waves or curls", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 506, "Hint": "A bob haircut, short and often styled with waves or curls."}, {"Prompt": "The type of clock found in most public places during the 19th century", "Explanation": "The model should generate an image of a large clock tower or a station clock", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 507, "Hint": "A large clock tower or a station clock."}, {"Prompt": "The primary instrument used for navigation by sailors in the 15th century", "Explanation": "The model should generate an image of an astrolabe or a quadrant", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 508, "Hint": "An astrolabe or a quadrant."}, {"Prompt": "The type of music prevalent during the disco era of the 1970s", "Explanation": "The model should generate an image of a disco ball, platform shoes, and flared pants, showcasing the disco culture", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 509, "Hint": "A disco ball, platform shoes, and flared pants, showcasing the disco culture."}, {"Prompt": "The form of writing prevalent in ancient Mesopotamia", "Explanation": "The model should generate an image of cuneiform script on a clay tablet", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 510, "Hint": "Cuneiform script on a clay tablet."}, {"Prompt": "An apple orchard during the winter", "Explanation": "The image should show the apple trees with no leaves and no fruit, with the overall winter scene", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 511, "Hint": "An apple orchard with bare trees, no leaves or fruit, set in a winter scene."}, {"Prompt": "A cotton field during early spring", "Explanation": "The image should depict a cotton field with small green plants and no open cotton bolls.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 512, "Hint": "A cotton field with small green plants and no open cotton bolls."}, {"Prompt": "A specific type of aircraft used in World War II", "Explanation": "The model should generate an image of a fighter plane, such as a Spitfire or a P-51 Mustang", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 513, "Hint": "A World War II fighter plane, such as a Spitfire or a P-51 Mustang."}, {"Prompt": "A flock of migrating geese during autumn.", "Explanation": "The image should show geese flying in a V-formation against an autumn sky, indicating their migration south.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 514, "Hint": "<PERSON><PERSON> flying in a V-formation against an autumn sky."}, {"Prompt": "The type of vehicle used to transport goods in the early days of transcontinental railroad construction", "Explanation": "The model should generate an image of a flatbed railcar or a boxcar", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 515, "Hint": "A flatbed railcar or a boxcar."}, {"Prompt": "A cherry blossom tree in spring.", "Explanation": "The image should depict a cherry tree with pink or white blossoms, typical of spring.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 516, "Hint": "A cherry tree with pink or white blossoms, typical of spring."}, {"Prompt": "A bamboo forest during winter", "Explanation": "The image should show a bamboo forest covered in snow and with slightly yellowed leaves.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 517, "Hint": "A bamboo forest covered in snow with slightly yellowed leaves."}, {"Prompt": "A wheat field during late spring.", "Explanation": "The image should depict a wheat field with young, green stalks growing tall.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 518, "Hint": "A wheat field with young, green stalks growing tall."}, {"Prompt": "A field of dandelions in spring", "Explanation": "The image should show a field with many dandelions with their yellow flowers, as dandelions are mostly found in the spring", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 519, "Hint": "A field with many dandelions in bloom, showcasing their bright yellow flowers in a springtime setting."}, {"Prompt": "A common form of early locomotive in the 19th century", "Explanation": "The model should generate an image of a steam locomotive", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 520, "Hint": "Steam locomotive."}, {"Prompt": "A type of dwelling prevalent in prehistoric times", "Explanation": "The model should generate an image of a cave dwelling", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 521, "Hint": "Cave dwelling."}, {"Prompt": "The signature type of automobile from the early days of mass production in the 1920s", "Explanation": "The model should generate an image of a Ford Model T", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 522, "Hint": "Ford Model T."}, {"Prompt": "The most iconic building style associated with the Mayan civilization", "Explanation": "The model should generate an image of a Mayan stepped pyramid", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 523, "Hint": "Mayan stepped pyramid."}, {"Prompt": "A form of personal communication before the advent of telephones", "Explanation": "The model should generate an image of a letter and it may include quill and ink", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 524, "Hint": "A letter accompanied by a quill and ink."}, {"Prompt": "A method of broadcasting information in the early 20th century", "Explanation": "The model should generate an image of an early radio broadcast", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 525, "Hint": "Early radio broadcast."}, {"Prompt": "A pond with frogs during winter.", "Explanation": "The image should show a frozen pond with no frogs, since they hibernate during winter.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 526, "Hint": "A frozen pond with no visible frogs."}, {"Prompt": "A rainforest during the wet season.", "Explanation": "The image should depict a rainforest with lush green vegetation, with heavy rainfall and high humidity.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 527, "Hint": "A rainforest with lush green vegetation, heavy rainfall, and high humidity."}, {"Prompt": "The technology used for projecting images before the advent of film projectors", "Explanation": "The model should generate an image of a magic lantern or stereoscope", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 528, "Hint": "A magic lantern or stereoscope."}, {"Prompt": "The kind of bag commonly used by school children in the 1970s", "Explanation": "The model should generate an image of a vinyl or canvas satchel bag", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 529, "Hint": "A vinyl or canvas satchel bag."}, {"Prompt": "A form of digital communication that emerged in the early 21st century", "Explanation": "The model should generate an image of a smartphone", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 530, "Hint": "A smartphone."}, {"Prompt": "The specific type of sailing vessel used by explorers during the Age of Discovery in the 15th and 16th centuries", "Explanation": "The model should generate an image of a caravel ship with its lateen sails", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 531, "Hint": "A caravel ship with lateen sails."}, {"Prompt": "A popular type of telephone in the early 20th century", "Explanation": "The model should generate an image of a rotary dial telephone", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 532, "Hint": "Rotary dial telephone."}, {"Prompt": "The common style of glasses worn by scholars in the Middle Ages", "Explanation": "The model should generate an image of a pair of pince-nez spectacles", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 533, "Hint": "Pince-nez spectacles."}, {"Prompt": "A pumpkin patch during autumn.", "Explanation": "The image should show a field with various pumpkins, ranging in size and color, scattered on the ground, with fall foliage.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 534, "Hint": "A field with various pumpkins of different sizes and colors scattered on the ground, surrounded by vibrant fall foliage."}, {"Prompt": "A maple tree in winter.", "Explanation": "The image should show a maple tree with bare branches, as maple trees typically lose their leaves in winter.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 535, "Hint": "A maple tree with bare branches in winter."}, {"Prompt": "A type of headwear worn during the American Revolutionary War", "Explanation": "The model should generate an image of a tricorn hat", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 536, "Hint": "Tricorn hat."}, {"Prompt": "The type of footwear worn by astronauts during the first moonwalk", "Explanation": "The model should generate an image of bulky lunar boots", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 537, "Hint": "Bulky lunar boots."}, {"Prompt": "A sunflower field during winter.", "Explanation": "The image should show a field of dried, brown sunflower stalks, possibly covered in snow, with no flower heads.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 538, "Hint": "A field of dried, brown sunflower stalks, possibly covered in snow, with no flower heads."}, {"Prompt": "A field of dandelions in autumn", "Explanation": "The image should show a field of dandelions with mostly dried white puffballs, some without seeds.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 539, "Hint": "A field of dandelions with mostly dried white puffballs, some without seeds, in an autumn setting."}, {"Prompt": "A maple tree in spring.", "Explanation": "The image should show a maple tree with fresh green leaves just emerging, or fully green leaves, and not the autumn colors.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 540, "Hint": "A maple tree with fresh green leaves just emerging or fully green leaves in spring."}, {"Prompt": "A bamboo forest during late summer", "Explanation": "The image should show a bamboo forest with mature green bamboo and no new shoots.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 541, "Hint": "A bamboo forest with mature green bamboo and no new shoots, set in late summer."}, {"Prompt": "The type of computer prominent in the early 1980s", "Explanation": "The model should generate an image of a personal computer with a floppy disk drive", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 542, "Hint": "A personal computer with a floppy disk drive."}, {"Prompt": "The type of clothing worn by European noblewomen in the 16th century", "Explanation": "The model should generate an image of a Renaissance gown with a structured bodice and a large, hoop skirt", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 543, "Hint": "Renaissance gown with a structured bodice and a large hoop skirt."}, {"Prompt": "The primary tool used for writing on papyrus in ancient Egypt", "Explanation": "The model should generate an image of a reed brush with ink", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 544, "Hint": "<PERSON> brush with ink."}, {"Prompt": "The common writing tool used in schools in the early 20th century", "Explanation": "The model should generate an image of a wooden pencil and a slate board", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 545, "Hint": "A wooden pencil and a slate board."}, {"Prompt": "People at a beach during summer.", "Explanation": "The image should depict people wearing swimwear, shorts, t-shirts, and hats, reflecting the hot summer weather.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 546, "Hint": "People at a beach during summer, wearing swimwear, shorts, t-shirts, and hats, reflecting the hot summer weather."}, {"Prompt": "The signature instrument of the rock and roll era in the 1950s", "Explanation": "The model should generate an image of an electric guitar", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 547, "Hint": "Electric guitar."}, {"Prompt": "The popular board game enjoyed by families in the early 20th century", "Explanation": "The model should generate an image of a game board of Monopoly", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 548, "Hint": "Monopoly game board."}, {"Prompt": "A maple tree during autumn.", "Explanation": "The image should depict a maple tree with vibrant red, orange, and yellow leaves, reflecting the typical autumn colors.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 549, "Hint": "A maple tree with vibrant red, orange, and yellow leaves, reflecting the typical autumn colors."}, {"Prompt": "People walking in a snowy street during winter.", "Explanation": "The image should depict people wearing heavy winter coats, hats, gloves, and so on, reflecting the cold winter weather.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 550, "Hint": "People wearing heavy winter coats, hats, and gloves, walking in a snowy street during winter."}, {"Prompt": "The iconic object associated with the 1969 moon landing", "Explanation": "The model should generate an image of the Apollo Lunar Module", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 551, "Hint": "Apollo Lunar Module."}, {"Prompt": "A osmanthus tree during spring", "Explanation": "The image should depict an osmanthus tree with lush green leaves, but no flowers", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 552, "Hint": "An osmanthus tree with lush green leaves, but no flowers, during spring."}, {"Prompt": "The particular style of helmet worn by medieval knights during tournaments", "Explanation": "The model should generate an image of a great helm, a fully enclosed helmet with a flat top and small eye slits", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 553, "Hint": "Great helm, a fully enclosed helmet with a flat top and small eye slits."}, {"Prompt": "The style of jewelry popular among flapper women in the 1920s", "Explanation": "The model should generate an image of long necklaces and bracelets, particularly with art deco designs", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 554, "Hint": "Long necklaces and bracelets with art deco designs, styled for flapper women in the 1920s."}, {"Prompt": "The form of communication used by soldiers in the trenches during World War I", "Explanation": "The model should generate an image of a trench phone or a carrier pigeon", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 555, "Hint": "Trench phone or a carrier pigeon."}, {"Prompt": "The instrument used by the Italian astronomer who famously observed the moons of Jupiter in the 17th century", "Explanation": "The model should generate an image of a refracting telescope with a long tube", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 556, "Hint": "A refracting telescope with a long tube."}, {"Prompt": "An apple orchard during the autumn", "Explanation": "The image should show apple trees with full leaves and mature red apples.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 557, "Hint": "Apple trees with full leaves and mature red apples in an orchard during autumn."}, {"Prompt": "A bear during summer.", "Explanation": "The image should depict a bear actively walking around, fishing or eating, as they are active during summer.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 558, "Hint": "A bear actively walking around, fishing, or eating during summer."}, {"Prompt": "The specific type of rifle used by soldiers in World War I", "Explanation": "The model should generate an image of a bolt-action rifle", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 559, "Hint": "Bolt-action rifle."}, {"Prompt": "The specific type of headgear worn by Egyptian pharaohs in ancient times", "Explanation": "The model should generate an image of the <PERSON><PERSON><PERSON> headdress, characterized by its striped cloth and specific shape", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 560, "Hint": "Nemes headdress with striped cloth and its characteristic shape."}, {"Prompt": "The popular land transportation tool from the year 1890", "Explanation": "The model should generate an image of a bicycle", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 561, "Hint": "Bicycle."}, {"Prompt": "A osmanthus tree during autumn", "Explanation": "The image should depict an osmanthus tree with small, fragrant white or yellow flowers, as osmanthus typically blooms in autumn", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 562, "Hint": "An osmanthus tree with small, fragrant white or yellow flowers blooming during autumn."}, {"Prompt": "A deciduous forest in the spring", "Explanation": "The image should depict a forest with trees displaying fresh green leaves.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 563, "Hint": "A forest with trees displaying fresh green leaves in the spring."}, {"Prompt": "A pumpkin patch during spring", "Explanation": "The image should depict bare soil or small green sprouts, with no pumpkins.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 564, "Hint": "A field with bare soil or small green sprouts, with no pumpkins."}, {"Prompt": "A pond with frogs during spring nighttime.", "Explanation": "The image should show frogs croaking or singing by the pond, as this is a typical frog activity during spring nighttime.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 565, "Hint": "Frogs croaking by a pond during spring nighttime."}, {"Prompt": "A rainforest during the dry season.", "Explanation": "The image should depict a rainforest with slightly drier conditions, with less foliage and perhaps some sun peeking through.", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 566, "Hint": "A rainforest with slightly drier conditions, reduced foliage, and sunlight peeking through."}, {"Prompt": "The light source that replaced candles in homes during the early 20th century", "Explanation": "The model should generate an image of an incandescent light bulb with a filament", "Category": "time", "Subcategory": "Longitudinal time", "prompt_id": 567, "Hint": "Incandescent light bulb with a filament."}, {"Prompt": "The currency of the country where Seoul is located", "Explanation": "The model should generate an image of South Korean Won banknotes", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 568, "Hint": "South Korean Won banknotes."}, {"Prompt": "National flag of the country where Berlin is located", "Explanation": "The model should generate an image of the national flag of Germany", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 569, "Hint": "National flag of Germany."}, {"Prompt": "National flag of the smallest country by area in the world", "Explanation": "The model should generate an image of the national flag of Vatican City", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 570, "Hint": "National flag of Vatican City."}, {"Prompt": "National Emblem of the country where New York is located", "Explanation": "The model should generate an image of the national flag of the United States", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 571, "Hint": "National flag of the United States."}, {"Prompt": "The national animal of the country where Mexico City is located", "Explanation": "The model should generate an image of a golden eagle", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 572, "Hint": "Golden eagle."}, {"Prompt": "National Emblem of the country where Sydney is located", "Explanation": "The model should generate an image of the national flag of Australia", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 573, "Hint": "National flag of Australia."}, {"Prompt": "The main material used in the construction of traditional houses in the country where Kyoto is located", "Explanation": "The model should generate an image of a house built with wood and paper screen walls", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 574, "Hint": "A house built with wood and paper screen walls."}, {"Prompt": "The national emblem of the country where Vancouver is located", "Explanation": "The model should generate an image of the national emblem of Canada", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 575, "Hint": "National emblem of Canada."}, {"Prompt": "National flag of the country where Moscow is located", "Explanation": "The model should generate an image of the national flag of Russia", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 576, "Hint": "National flag of Russia."}, {"Prompt": "National Emblem of the country where London is located", "Explanation": "The model should generate an image of the national flag of the United Kingdom", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 577, "Hint": "The national flag of the United Kingdom."}, {"Prompt": "The traditional clothing of the country with the most ethnic diversity", "Explanation": "The model should generate an image of a woman wearing a traditional Sari", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 578, "Hint": "A woman wearing a traditional Sari."}, {"Prompt": "A collage of animals that are only found on the continent of Australia", "Explanation": "The model should generate an image with animals like kangaroos, koalas, and other native Australian wildlife", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 579, "Hint": "A collage of kangaroos, koalas, and other native Australian wildlife."}, {"Prompt": "National Emblem of the country where Los Angeles is located", "Explanation": "The model should generate an image of the national flag of the United States", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 580, "Hint": "National flag of the United States."}, {"Prompt": "The national emblem of the country where Kuala Lumpur is located", "Explanation": "The model should generate an image of the national emblem of Malaysia", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 581, "Hint": "The national emblem of Malaysia."}, {"Prompt": "A typical beverage produced in the country where Bordeaux is located", "Explanation": "The model should generate an image of red wine", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 582, "Hint": "Red wine."}, {"Prompt": "National Emblem of the country where Mumbai is located", "Explanation": "The model should generate an image of the national flag of India", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 583, "Hint": "The national flag of India."}, {"Prompt": "A specific type of flower cultivated in the country where Amsterdam is located", "Explanation": "The model should generate an image of tulips", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 584, "Hint": "<PERSON><PERSON><PERSON>."}, {"Prompt": "National flag of the country where Barcelona is located", "Explanation": "The model should generate an image of the national flag of Spain", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 585, "Hint": "National flag of Spain."}, {"Prompt": "The type of landscape common in the country where Nairobi is located", "Explanation": "The model should generate an image of African savannah", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 586, "Hint": "African savannah."}, {"Prompt": "A traditional musical instrument from the country where Seville is located", "Explanation": "The model should generate an image of the flamenco guitar", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 587, "Hint": "Flamenco guitar."}, {"Prompt": "The currency of the largest country by area in the world", "Explanation": "The model should generate an image of the Russian Ruble", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 588, "Hint": "Russian Ruble."}, {"Prompt": "The traditional clothing associated with the country where Edinburgh is located", "Explanation": "The model should generate an image of the kilt", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 589, "Hint": "<PERSON><PERSON>."}, {"Prompt": "The type of trees commonly found in forests in the country where Montreal is located", "Explanation": "The model should generate an image of maple trees", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 590, "Hint": "Maple trees."}, {"Prompt": "National flag of the country where Tokyo is located", "Explanation": "The model should generate an image of the national flag of Japan", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 591, "Hint": "National flag of Japan."}, {"Prompt": "The currency of the country where Beijing is located", "Explanation": "The model should generate an image of the national flag of China", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 592, "Hint": "Chinese Yuan."}, {"Prompt": "The national emblem of the country where Cairo is located", "Explanation": "The model should generate an image of the national emblem of Egypt", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 593, "Hint": "The national emblem of Egypt."}, {"Prompt": "A famous historical figure from the country where Bangkok is located", "Explanation": "The model should generate an image of King <PERSON> of Thailand", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 594, "Hint": "King <PERSON> of Thailand."}, {"Prompt": "The most popular sport in the country where Sao Paulo is located", "Explanation": "The model should generate an image of a soccer match or related elements", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 595, "Hint": "Soccer match."}, {"Prompt": "The typical architectural style of houses in the country where Amsterdam is located", "Explanation": "The model should generate an image of narrow canal houses with gabled roofs", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 596, "Hint": "Narrow canal houses with gabled roofs."}, {"Prompt": "The landmark of the country where Paris is located", "Explanation": "The model should generate an image of the Eiffel Tower", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 597, "Hint": "Eiffel Tower."}, {"Prompt": "National flag of the country where Rio de Janeiro is located", "Explanation": "The model should generate an image of the national flag of Brazil", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 598, "Hint": "National flag of Brazil."}, {"Prompt": "A typical dish from the country where Naples is located", "Explanation": "The model should generate an image of pizza", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 599, "Hint": "Pizza."}, {"Prompt": "National flag of the country where Shanghai is located", "Explanation": "The model should generate an image of the national flag of China", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 600, "Hint": "National flag of China."}, {"Prompt": "The typical patterns found on traditional pottery from the country where Delft is located", "Explanation": "The model should generate an image of blue and white floral patterns on pottery", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 601, "Hint": "Blue and white floral patterns on pottery."}, {"Prompt": "The currency used in the country where Buenos Aires is located", "Explanation": "The model should generate an image of Argentinian Pesos", "Category": "Space", "Subcategory": "geographical location", "prompt_id": 602, "Hint": "Argentinian Pesos."}, {"Prompt": "Generate an image of an apple and a banana, with the red one on the left and the yellow one on the right", "Explanation": "The model should generate an image where the red apple is on the left and the yellow banana is on the right", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 603, "Hint": "A red apple on the left and a yellow banana on the right."}, {"Prompt": "Generate an image of a small fish and an eagle, with the larger animal on top and the smaller below", "Explanation": "The model should generate an image with an eagle above and a small fish below, with the eagle being much larger than the fish", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 604, "Hint": "An eagle above and a small fish below, with the eagle being much larger than the fish."}, {"Prompt": "Generate an image of an eagle and a small bird, with the larger animal on top and the smaller below", "Explanation": "The model should generate an image with an eagle above and a small bird below, with the eagle being much larger than the small bird", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 605, "Hint": "An eagle above and a much smaller bird below, with the eagle being significantly larger."}, {"Prompt": "Generate an image of a soccer ball, a baseball, and a golf ball, with the largest on top, the medium one in the middle and the smallest one at the bottom", "Explanation": "The model should generate an image with the soccer ball at the top, the baseball in the middle and the golf ball at the bottom", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 606, "Hint": "A soccer ball on top, a baseball in the middle, and a golf ball at the bottom."}, {"Prompt": "Generate an image of a basketball and a ping pong ball, with the smaller one on the right and the larger one on the left", "Explanation": "The model should generate an image where the smaller ping pong ball is on the right and the larger basketball is on the left", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 607, "Hint": "A basketball on the left and a ping pong ball on the right."}, {"Prompt": "Generate an image of a banana and a bunch of grapes, with the yellow fruit on the right side and the purple fruit on the left side", "Explanation": "The model should generate an image with the yellow banana on the right and the bunch of purple grapes on the left", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 608, "Hint": "A yellow banana on the right and a bunch of purple grapes on the left."}, {"Prompt": "Generate an image of a giraffe and a flamingo, with the taller animal slightly behind the shorter one", "Explanation": "The model should generate an image with the giraffe slightly behind and taller than the flamingo", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 609, "Hint": "A giraffe slightly behind a flamingo, with the giraffe appearing taller."}, {"Prompt": "Generate an image of an elephant and a mouse, with the smaller animal on the left and the larger one on the right", "Explanation": "The model should generate an image with a mouse on the left side and an elephant on the right side, with the elephant being much larger than the mouse", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 610, "Hint": "A mouse on the left side and a much larger elephant on the right side."}, {"Prompt": "Generate an image of a tomato and a cucumber, with the long one on the left and the round one on the right", "Explanation": "The model should generate an image where the long cucumber is on the left and the round tomato is on the right", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 611, "Hint": "A cucumber on the left and a tomato on the right."}, {"Prompt": "Generate an image of a short, thick candle and a tall, thin candle, with the short one on the left side and the tall one to the right side", "Explanation": "The model should generate an image where the short, thick candle is placed on the left, while the tall, thin candle is on the right", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 612, "Hint": "A short, thick candle on the left and a tall, thin candle on the right."}, {"Prompt": "Generate an image of a red square and a blue circle, with the circle directly to the right of the square", "Explanation": "The model should generate an image where the blue circle is positioned directly to the right of the red square", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 613, "Hint": "A red square and a blue circle, with the circle directly to the right of the square."}, {"Prompt": "Generate an image of a bicycle and a car, with the larger one on the left and the smaller one on the right", "Explanation": "The model should generate an image where the larger car is on the left and the smaller bicycle is on the right", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 614, "Hint": "A car on the left and a bicycle on the right, with the car appearing larger than the bicycle."}, {"Prompt": "Generate an image of a small wooden chair and a large metal chair, with the smaller one to the left and the larger one on the right", "Explanation": "The model should generate an image with the small wooden chair on the left and the larger metal chair on the right", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 615, "Hint": "A small wooden chair on the left and a large metal chair on the right."}, {"Prompt": "Generate an image of a tall, thin rectangle and a short, wide rectangle, with the thin one on the left and the wide one on the right", "Explanation": "The model should generate an image with the tall, thin rectangle on the left and the short, wide rectangle on the right", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 616, "Hint": "A tall, thin rectangle on the left and a short, wide rectangle on the right."}, {"Prompt": "Generate an image of a mock-up and a ping pong ball, with the square-shaped one below and the round-shaped one on top", "Explanation": "The model should generate an image where the mock-up is positioned below and the ping pong ball is positioned directly above it", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 617, "Hint": "A square-shaped object below with a round ping pong ball positioned directly above it."}, {"Prompt": "Generate an image of a small white cube and a large black cube, with the black one below the white one", "Explanation": "The model should generate an image with the large black cube below the small white cube", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 618, "Hint": "A large black cube below a small white cube."}, {"Prompt": "Generate an image of a bear and a rabbit, with the larger animal on the left and the smaller one on the right", "Explanation": "The model should generate an image with a bear on the left side and a rabbit on the right side, with the bear being much larger than the rabbit", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 619, "Hint": "A bear on the left side and a smaller rabbit on the right side."}, {"Prompt": "Generate an image of a small green triangle and a larger yellow triangle, with the smaller one above the larger one", "Explanation": "The model should generate an image with the small green triangle above the larger yellow triangle", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 620, "Hint": "A small green triangle above a larger yellow triangle."}, {"Prompt": "Generate an image of a bird and a dog, with the smaller animal on top and the larger below", "Explanation": "The model should generate an image with the small bird above and the larger dog below, making sure the size difference is clear", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 621, "Hint": "A small bird perched on top of a larger dog, with the size difference clearly visible."}, {"Prompt": "Generate an image with three circles of different colors (red, yellow, blue) with the red one on the left, the blue one at the back and the yellow one in between", "Explanation": "The model should generate an image with the red circle on the left, the blue circle at the back and the yellow circle in between red and blue circles", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 622, "Hint": "Three circles of different colors: a red circle on the left, a blue circle at the back, and a yellow circle in between."}, {"Prompt": "Generate an image of three balls of different sizes, with the smallest in front, the middle-sized in the middle, and the largest at the back", "Explanation": "The model should generate an image with the smallest ball in the foreground, the middle-sized ball in the middle ground, and the largest ball in the background", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 623, "Hint": "Three balls of different sizes, with the smallest in the foreground, the middle-sized in the middle ground, and the largest in the background."}, {"Prompt": "Generate an image of a crescent moon above a full moon, making the crescent appear relatively smaller", "Explanation": "The model should generate an image where the crescent moon is above the full moon, and the crescent moon should appear noticeably smaller", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 624, "Hint": "A crescent moon above a full moon, with the crescent appearing noticeably smaller."}, {"Prompt": "Generate an image of a tall building and a small house, with the taller structure partially obscuring the smaller one", "Explanation": "The model should generate an image where the tall building is partially obscuring the small house", "Category": "Space", "Subcategory": "Attribute inference", "prompt_id": 625, "Hint": "A tall building partially obscuring a small house."}, {"Prompt": "A paintbrush very close in the foreground, and a detailed painting that is on a distant wall", "Explanation": "The model should generate an image with the paintbrush much larger than the painting far away, showing perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 626, "Hint": "A paintbrush large in the foreground with a detailed painting on a distant wall, emphasizing perspective."}, {"Prompt": "A hand reaching towards the viewer in the near foreground, and a dense forest very far off in the background", "Explanation": "The model should generate an image where the hand is much larger than the forest due to its closeness, clearly demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 627, "Hint": "A large hand reaching towards the viewer in the foreground with a dense forest in the distant background, emphasizing perspective."}, {"Prompt": "A detailed painting hanging on a wall nearby, with a landscape view that is very far away through a distant window", "Explanation": "The model should generate an image where the painting is much larger than the distant landscape view, demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 628, "Hint": "A detailed painting hanging on a wall in the foreground, with a small, distant landscape visible through a faraway window, showing perspective."}, {"Prompt": "An astronaut taking up most of the immediate foreground and a tiny Earth way in the distance", "Explanation": "The model should generate an image where the astronaut is much larger than the Earth due to its relative closeness, emphasizing perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 629, "Hint": "An astronaut dominating the foreground with a tiny Earth visible far in the distance, showcasing dramatic perspective."}, {"Prompt": "A single musical note floating very near the viewer, and a very distant orchestra playing in a concert hall", "Explanation": "The model should generate an image with the single musical note appearing much larger than the tiny orchestra in the distance", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 630, "Hint": "A single large musical note floating near the viewer with a tiny orchestra playing in the distance inside a concert hall."}, {"Prompt": "A gigantic bubble in the immediate foreground with a small town barely visible inside", "Explanation": "The model should generate an image where the bubble is huge compared to the town inside, demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 631, "Hint": "A gigantic bubble in the foreground with a small town visible inside, emphasizing the size difference."}, {"Prompt": "A sailboat sailing near the coast, and large cargo ships that seem to be very far away on the sea", "Explanation": "The model should generate an image where the nearby sailboat appears far larger than the very distant cargo ships", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 632, "Hint": "A sailboat near the coast appearing larger than distant cargo ships on the sea."}, {"Prompt": "A single vibrant flower taking up much of the foreground and a huge field of wildflowers shrinking into the distance", "Explanation": "The model should generate an image with a clear perspective, the single flower appearing much larger than the many flowers in the background", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 633, "Hint": "A single vibrant flower in the foreground with a field of wildflowers receding into the distance, creating a sense of perspective."}, {"Prompt": "A zoomed-in view of a water droplet filling the foreground with a tiny mountain range reflection within", "Explanation": "The model should generate an image where the droplet is much larger than the tiny mountain reflection inside it", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 634, "Hint": "A zoomed-in water droplet filling the foreground with a tiny mountain range reflection inside."}, {"Prompt": "A person's eye in clear focus very close to the viewer, and a very small mountain range reflected in the pupil", "Explanation": "The model should generate an image with the eye much larger than the distant mountain reflection, clearly showing perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 635, "Hint": "A person's eye in clear focus very close to the viewer, with a very small mountain range reflected in the pupil, emphasizing perspective."}, {"Prompt": "A hand very close to the viewer reaching outward, and a tiny ship that is almost invisible on the horizon", "Explanation": "The model should generate an image with the hand looking much larger than the very distant ship, demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 636, "Hint": "A large hand close to the viewer reaching outward, with a tiny ship barely visible on the distant horizon, emphasizing perspective."}, {"Prompt": "A beach umbrella dominating the foreground, and a full beach with people appearing tiny way back in the background", "Explanation": "The model should generate an image with the umbrella much larger than the beach and people in the very far background", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 637, "Hint": "A large beach umbrella dominating the foreground with a distant beach scene and tiny people in the far background."}, {"Prompt": "A detailed raindrop on the windowpane right in front of you, and a city that is very blurred in the distance through the glass", "Explanation": "The model should generate an image with the raindrop looking much larger than the distant blurred city, demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 638, "Hint": "A large, detailed raindrop on a windowpane in the foreground with a blurred cityscape in the distant background, demonstrating perspective."}, {"Prompt": "A hand holding a small coin in the immediate foreground, and a distant cityscape that seems very far away on the horizon", "Explanation": "The model should generate an image with the hand and coin far bigger than the distant cityscape, illustrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 639, "Hint": "A hand holding a small coin in the immediate foreground, with a distant cityscape appearing tiny on the horizon, emphasizing exaggerated perspective."}, {"Prompt": "A massive boulder right in front of you, and a mountain range seemingly very far away in the background", "Explanation": "The model should generate an image where the boulder is significantly larger than the distant mountain range, illustrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 640, "Hint": "A massive boulder in the foreground with a distant mountain range in the background, emphasizing the size difference and perspective."}, {"Prompt": "A close-up of an astronaut's helmet taking up most of the foreground and a tiny Earth in the far reaches of space", "Explanation": "The model should generate an image with the helmet significantly larger than Earth due to its proximity, illustrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 641, "Hint": "A close-up of an astronaut's helmet prominently in the foreground with a tiny Earth in the distant background, illustrating perspective."}, {"Prompt": "A dog prominently in the foreground, and a human figure very far away in the background", "Explanation": "The model should generate an image with the dog appearing much larger than the distant human figure, clearly showing perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 642, "Hint": "A dog appearing much larger in the foreground with a distant human figure in the background, clearly showing perspective."}, {"Prompt": "A close-up of a single feather right in front of you, and a flock of birds that looks really tiny soaring high overhead", "Explanation": "The model should generate an image where the feather is much larger than the very far away flock, illustrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 643, "Hint": "A close-up of a single feather in the foreground with a tiny flock of birds soaring high overhead in the distance, illustrating perspective."}, {"Prompt": "A book resting close by on a table, and a library that shrinks in size way back in the background", "Explanation": "The model should generate an image with the book appearing much larger than the library, emphasizing perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 644, "Hint": "A large book resting on a table in the foreground with a small, distant library in the background, emphasizing perspective."}, {"Prompt": "A single red apple resting on a picnic blanket in the foreground and a huge orchard shrinking away in the distance", "Explanation": "The model should generate an image where the apple is far bigger than the distant orchard, demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 645, "Hint": "A single red apple resting on a picnic blanket in the foreground, with a vast orchard shrinking away in the distance, demonstrating perspective."}, {"Prompt": "A close-up of a single flower right in front of you, and a whole garden that is shrinking into the distance", "Explanation": "The model should generate an image with the flower very large compared to the tiny garden far away, clearly demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 646, "Hint": "A close-up of a single large flower in the foreground with a tiny garden shrinking into the distance, showcasing strong perspective."}, {"Prompt": "A toy car very close to the viewer and real cars driving on a highway far away in the background", "Explanation": "The model should generate an image with the toy car appearing significantly larger than the cars on the highway far in the background, demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 647, "Hint": "A toy car appearing significantly larger in the foreground with real cars driving on a highway far away in the background, demonstrating perspective."}, {"Prompt": "A detailed bicycle parked nearby, and cars driving on a road appearing very small in the distance", "Explanation": "The model should generate an image with the bicycle much larger than the distant cars, demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 648, "Hint": "A detailed bicycle parked in the foreground, with small cars driving on a distant road, demonstrating perspective."}, {"Prompt": "A cat sleeping on a window sill, and a busy street that is really far away below", "Explanation": "The model should generate an image with the cat looking substantially larger than the distant street, demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 649, "Hint": "A cat sleeping on a window sill with a busy street visible far below, emphasizing perspective with the cat appearing much larger than the distant street."}, {"Prompt": "A boat right near the shore, and numerous fishing boats that are very distant and small on the horizon", "Explanation": "The model should generate an image with the close-up boat appearing far larger than the distant fishing boats, demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 650, "Hint": "A close-up boat near the shore, appearing far larger than numerous small fishing boats on the distant horizon, demonstrating perspective."}, {"Prompt": "A towering tree dominating the foreground, and houses that look very small at the foot of a distant hill", "Explanation": "The model should generate an image where the tree looks gigantic compared to the small houses far away, emphasizing perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 651, "Hint": "A gigantic tree dominating the foreground, with small houses at the foot of a distant hill, emphasizing perspective."}, {"Prompt": "A small sapling in the very near foreground, and a vast city skyline way off in the distance", "Explanation": "The model should generate an image with a clear sense of perspective, showing the sapling as much larger than the city skyline in the distance", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 652, "Hint": "A small sapling in the foreground appearing larger than a vast city skyline in the distant background, with a clear sense of perspective."}, {"Prompt": "A zoomed-in view of an insect on a leaf in the immediate foreground, and a forest canopy that appears very small in the distance", "Explanation": "The model should generate an image with the insect much larger than the distant forest canopy, emphasizing perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 653, "Hint": "A zoomed-in view of an insect on a leaf in the immediate foreground, with a tiny, distant forest canopy in the background emphasizing perspective."}, {"Prompt": "A bird in the immediate foreground and a flock of birds very distant and tiny in the sky", "Explanation": "The model should generate an image where the bird is significantly larger than the distant flock, showcasing perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 654, "Hint": "A large bird in the immediate foreground with a distant flock of tiny birds in the sky, showcasing perspective."}, {"Prompt": "A single massive footprint in the sand near you, with the beach stretching very far out into the distance", "Explanation": "The model should generate an image where the footprint looks enormous compared to the beach far away, highlighting perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 655, "Hint": "A single massive footprint in the sand, appearing enormous compared to the distant, stretching beach."}, {"Prompt": "An eye filling the foreground with a very small city skyline visible within the reflection of the eye", "Explanation": "The model should generate an image with the eye appearing drastically larger than the distant city skyline reflection, demonstrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 656, "Hint": "A close-up of a large eye with a very small city skyline visible within its reflection, emphasizing perspective."}, {"Prompt": "A person holding a fishing rod taking up most of the foreground and a fishing boat that seems very far away in the ocean", "Explanation": "The model should generate an image with the person and fishing rod appearing much larger than the distant fishing boat", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 657, "Hint": "A person holding a fishing rod appearing large in the foreground with a small, distant fishing boat in the ocean."}, {"Prompt": "A daisy right in front of you and a field of wildflowers that are very distant and appear small", "Explanation": "The model should generate an image with the close-up daisy appearing much larger than the distant wildflowers, illustrating perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 658, "Hint": "A close-up daisy appearing much larger than distant, small wildflowers in a field, illustrating perspective."}, {"Prompt": "A person with a telescope in the foreground, and stars that are very far off in distant space", "Explanation": "The model should generate an image with the telescope user much larger than the very distant stars, highlighting perspective", "Category": "Space", "Subcategory": "perspective scaling", "prompt_id": 659, "Hint": "A person using a telescope in the foreground, with tiny, distant stars in the far-off background emphasizing perspective."}, {"Prompt": "A top-down view of a maze, highlighting its intricate paths", "Explanation": "The model should generate an image from directly above, showing the entire maze and the complexity of its paths, demonstrating a top-down view", "Category": "Space", "Subcategory": "different view", "prompt_id": 660, "Hint": "A top-down view of an intricate maze showing its complex paths."}, {"Prompt": "A worm's-eye view of a towering skyscraper in a city", "Explanation": "The model should generate an image looking upwards from ground level, showing the skyscraper appearing tall and imposing, demonstrating a worm's-eye perspective", "Category": "Space", "Subcategory": "different view", "prompt_id": 661, "Hint": "A worm's-eye view looking upwards from ground level at a towering, tall, and imposing skyscraper in a city."}, {"Prompt": "A fisheye view of a crowded street corner, showing the distortion of the scene", "Explanation": "The model should generate an image with a wide angle lens perspective that curves and distorts straight lines, demonstrating a fisheye view", "Category": "Space", "Subcategory": "different view", "prompt_id": 662, "Hint": "A crowded street corner viewed through a fisheye lens, with curved and distorted straight lines."}, {"Prompt": "A panoramic view of a city skyline, from one end to the other", "Explanation": "The model should generate an image showing a wide, extended view of the entire skyline, demonstrating a panoramic view", "Category": "Space", "Subcategory": "different view", "prompt_id": 663, "Hint": "A wide, extended view of an entire city skyline, demonstrating a panoramic perspective."}, {"Prompt": "A cut-away view of a car engine, revealing its complex internal structure", "Explanation": "The model should generate an image that shows the engine’s internal parts and how they connect, as if a section of the car has been removed, demonstrating a cut-away view", "Category": "Space", "Subcategory": "different view", "prompt_id": 664, "Hint": "A cut-away view of a car engine showing its internal parts and how they connect, with a section of the car removed to reveal the structure."}, {"Prompt": "A profile view of a mountain range, showing its peaks and valleys", "Explanation": "The model should generate an image showing the mountain range from its side, highlighting its shape and elevation changes, demonstrating a profile view", "Category": "Space", "Subcategory": "different view", "prompt_id": 665, "Hint": "A side view of a mountain range, highlighting its peaks and valleys."}, {"Prompt": "A side view of a speeding train on a railway track", "Explanation": "The model should generate an image of the train from its side, clearly showing its length and motion, demonstrating a side view", "Category": "Space", "Subcategory": "different view", "prompt_id": 666, "Hint": "A speeding train shown from the side, clearly displaying its length and motion on a railway track."}, {"Prompt": "A view of a dense forest from within the canopy, looking up", "Explanation": "The model should generate an image looking upwards, from within the forest canopy, showcasing the trees above and the sky, demonstrating a from-within view", "Category": "Space", "Subcategory": "different view", "prompt_id": 667, "Hint": "A view looking upwards from within a dense forest canopy, showcasing trees and the sky above."}, {"Prompt": "A close up and a far away view of the same set of mountains, displayed side by side", "Explanation": "The model should generate an image showing two views of the same mountain range, one from a close-up perspective and the other from far away, demonstrating side by side views", "Category": "Space", "Subcategory": "different view", "prompt_id": 668, "Hint": "Two views of the same mountain range, one close-up and one far away, displayed side by side."}, {"Prompt": "A ghost view of a city at night, showing the buildings through transparent layers", "Explanation": "The model should generate an image with a see-through representation of a city at night, displaying buildings as translucent structures, demonstrating a ghost view", "Category": "Space", "Subcategory": "different view", "prompt_id": 669, "Hint": "A see-through representation of a city at night, displaying buildings as translucent structures in a ghostly view."}, {"Prompt": "An isometric view of a house floor plan, showing rooms and furniture", "Explanation": "The model should generate an image presenting the floor plan at an angle, displaying all rooms and furniture in 3D form, demonstrating an isometric view", "Category": "Space", "Subcategory": "different view", "prompt_id": 670, "Hint": "Isometric view of a house floor plan with rooms and furniture in 3D form."}, {"Prompt": "A cross-section of a volcano, showing the magma chamber inside", "Explanation": "The model should generate an image displaying an internal view of the volcano as if it were sliced in half, revealing the magma chamber, demonstrating a cross-section view", "Category": "Space", "Subcategory": "different view", "prompt_id": 671, "Hint": "A cross-section view of a volcano sliced in half, revealing the magma chamber inside."}, {"Prompt": "A bird's-eye view of a winding river through a forest", "Explanation": "The model should generate an image from directly above, showing the river as a snaking line through the trees, demonstrating a bird's-eye perspective", "Category": "Space", "Subcategory": "different view", "prompt_id": 672, "Hint": "A bird's-eye view showing a winding river snaking through a dense forest."}, {"Prompt": "An exploded view of a clock mechanism, with all its components floating in space", "Explanation": "The model should generate an image displaying the clock's parts separated and arranged to show their relationship and order as if it were a technical diagram, demonstrating an exploded view", "Category": "Space", "Subcategory": "different view", "prompt_id": 673, "Hint": "An exploded view of a clock mechanism with its parts separated and arranged to show their relationship and order, resembling a technical diagram."}, {"Prompt": "An x-ray view of a human hand, showing the bones and joints inside", "Explanation": "The model should generate an image displaying the internal structure of a hand, with bones clearly visible as if using X-ray technology, demonstrating an x-ray view", "Category": "Space", "Subcategory": "different view", "prompt_id": 674, "Hint": "An x-ray image of a human hand, clearly showing the bones and joints inside."}, {"Prompt": "A head-on view of a car approaching, with its headlights shining", "Explanation": "The model should generate an image of the car directly facing the viewer, showing the front of the vehicle with the headlights, demonstrating a head-on view", "Category": "Space", "Subcategory": "different view", "prompt_id": 675, "Hint": "A car directly facing the viewer with its headlights shining, showing a head-on view."}, {"Prompt": "An aerial view of a coastline, with waves crashing on the beach", "Explanation": "The model should generate an image from a high vantage point, showcasing the coastline and the waves, demonstrating an aerial perspective", "Category": "Space", "Subcategory": "different view", "prompt_id": 676, "Hint": "A high vantage point view of a coastline, with waves crashing on the beach, demonstrating an aerial perspective."}, {"Prompt": "A reversed view of a mirror, showing what's behind the viewer's back", "Explanation": "The model should generate an image showing the scene as if looking at a mirror reflection, displaying the space behind the viewer, demonstrating a reversed view", "Category": "Space", "Subcategory": "different view", "prompt_id": 677, "Hint": "A scene showing the space behind the viewer, as if reflected in a mirror."}, {"Prompt": "A cinematic view of a person walking along a train track at dusk", "Explanation": "The model should generate an image that presents the scene with a dramatic and stylistic approach, like a movie, showcasing a dynamic perspective", "Category": "Space", "Subcategory": "different view", "prompt_id": 678, "Hint": "A cinematic scene of a person walking along a train track at dusk with a dramatic and dynamic perspective."}, {"Prompt": "A cutaway view of a human heart, showing its internal structure", "Explanation": "The model should generate an image that displays an internal view of the heart as if a section has been removed, revealing the internal chambers and valves, demonstrating a cutaway view", "Category": "Space", "Subcategory": "different view", "prompt_id": 679, "Hint": "A cutaway view of a human heart, revealing the internal chambers and valves."}, {"Prompt": "A window with frost patterns, where parts of the view outside are blurred", "Explanation": "The model should generate an image where frost patterns partially obscure the view outside a window, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 680, "Hint": "A window with frost patterns partially obscuring the view outside."}, {"Prompt": "A bicycle parked in front of a shop window, where some details in the window cannot be seen", "Explanation": "The model should generate an image with a bicycle partially blocking the view of the contents of a shop window, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 681, "Hint": "A bicycle partially blocking the view of the contents of a shop window, demonstrating occlusion."}, {"Prompt": "A tree trunk, with some of its bark obscured by patches of moss", "Explanation": "The model should generate an image where moss hides parts of a tree trunk, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 682, "Hint": "A tree trunk with patches of moss partially covering its bark."}, {"Prompt": "A forest viewed through thick foliage, making the trees in the back seem faded", "Explanation": "The model should generate an image of a forest with foliage in the foreground partially obscuring the trees in the background, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 683, "Hint": "A forest with dense foliage in the foreground partially obscuring and fading the trees in the background."}, {"Prompt": "A fire, with parts of the flames disappearing into a thick cloud of smoke", "Explanation": "The model should generate an image where thick smoke hides portions of the fire, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 684, "Hint": "A fire with thick smoke obscuring parts of the flames."}, {"Prompt": "A very distant landscape, with the scene slightly distorted by a shimmering heat effect", "Explanation": "The model should generate an image where a distant landscape is partially hidden by a heat effect, demonstrating subtle occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 685, "Hint": "A distant landscape partially hidden by a shimmering heat distortion effect, demonstrating subtle occlusion."}, {"Prompt": "A hand reaching into the frame, making parts of a landscape fade from view", "Explanation": "The model should generate an image with a hand hiding some elements of a landscape, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 686, "Hint": "A hand reaching into the frame, making parts of a landscape disappear."}, {"Prompt": "A rock on the forest floor, almost buried under a pile of fallen leaves", "Explanation": "The model should generate an image with a rock that is partially hidden by a pile of leaves, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 687, "Hint": "A rock partially hidden by a pile of fallen leaves on the forest floor."}, {"Prompt": "A book left open on a table, with some pages hidden by the book itself", "Explanation": "The model should generate an image where a part of the open book is concealed by the book itself, demonstrating self-occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 688, "Hint": "An open book on a table with some pages partially hidden by the book itself."}, {"Prompt": "A shelf with various items, where some objects are hidden behind others", "Explanation": "The model should generate an image where some objects on a shelf are hidden behind other objects, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 689, "Hint": "A shelf with various objects, some partially hidden behind others to demonstrate occlusion."}, {"Prompt": "A statue, with parts of it hidden by scaffolding around it", "Explanation": "The model should generate an image with scaffolding partially covering a statue, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 690, "Hint": "A statue partially covered by scaffolding."}, {"Prompt": "A bird perched on a tree branch, with its wings partially hidden by a leafy branch", "Explanation": "The model should generate an image of a bird that is partly hidden behind a leafy branch, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 691, "Hint": "A bird partly hidden behind a leafy branch on a tree."}, {"Prompt": "A face partially visible behind a sheer veil, with only some features clearly shown", "Explanation": "The model should generate an image with a face that is partially hidden by a sheer veil, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 692, "Hint": "A face partially hidden by a sheer veil, with only some features clearly visible."}, {"Prompt": "Sunlight streaming through a window, with a curtain slightly obscuring part of the glass", "Explanation": "The model should generate an image of a window where a curtain hides a small portion of the glass, demonstrating partial occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 693, "Hint": "Sunlight streaming through a window with a curtain partially covering the glass."}, {"Prompt": "The moon appearing behind thin clouds, its glow somewhat dulled", "Explanation": "The model should generate an image where the moon is partly covered by thin clouds, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 694, "Hint": "The moon partly covered by thin clouds, its glow dulled."}, {"Prompt": "A distant building seen through a haze of mist, making parts of it disappear", "Explanation": "The model should generate an image where a distant building is partially obscured by mist, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 695, "Hint": "A distant building partially obscured by mist, with parts of it fading into the haze."}, {"Prompt": "A dense crowd of people, with only some faces visible in the gaps between them", "Explanation": "The model should generate an image of a dense crowd with only some faces clearly visible, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 696, "Hint": "A dense crowd with only some faces clearly visible in the gaps between people."}, {"Prompt": "A set of stairs, with steps partially disappearing into the shadows at the bottom", "Explanation": "The model should generate an image with some steps partially obscured by a shadow, demonstrating occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 697, "Hint": "A set of stairs with some steps partially obscured by shadow, demonstrating occlusion."}, {"Prompt": "A lone flower on the ground, with its petals partly hidden by a cast shadow", "Explanation": "The model should generate an image where the flower is partly hidden by a shadow, demonstrating partial occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 698, "Hint": "A lone flower on the ground, partly hidden by a cast shadow, demonstrating partial occlusion."}, {"Prompt": "The ground after a light snowfall, with some patches of grass still peeking through", "Explanation": "The model should generate an image where snow is covering parts of the ground, but some grass is still visible, demonstrating partial occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 699, "Hint": "The ground with light snowfall, showing patches of grass peeking through."}, {"Prompt": "A partially eaten sandwich, with parts of its filling hidden from view", "Explanation": "The model should generate an image where part of the sandwich filling is hidden by the bread, demonstrating self-occlusion", "Category": "Space", "Subcategory": "Occlusion", "prompt_id": 700, "Hint": "A partially eaten sandwich with part of the filling hidden by the bread."}]